'use client'

import { useState } from 'react'
import { 
  Palette, 
  Type, 
  Layout, 
  Zap,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Info,
  Star,
  Heart,
  Bookmark
} from 'lucide-react'

interface ColorSwatch {
  name: string
  variable: string
  hex: string
  usage: string
}

interface TypographyExample {
  className: string
  label: string
  example: string
}

export default function DesignSystem() {
  const [activeTab, setActiveTab] = useState<'colors' | 'typography' | 'components'>('colors')

  const colorPalette: ColorSwatch[] = [
    { name: 'Primary 50', variable: '--primary-50', hex: '#f0fdf4', usage: 'Light backgrounds, subtle highlights' },
    { name: 'Primary 100', variable: '--primary-100', hex: '#dcfce7', usage: 'Light backgrounds, cards' },
    { name: 'Primary 500', variable: '--primary-500', hex: '#22c55e', usage: 'Main brand color, primary actions' },
    { name: 'Primary 600', variable: '--primary-600', hex: '#16a34a', usage: 'Hover states, emphasis' },
    { name: 'Primary 700', variable: '--primary-700', hex: '#15803d', usage: 'Active states, dark themes' },
    { name: 'Success 500', variable: '--success-500', hex: '#22c55e', usage: 'Success messages, positive actions' },
    { name: 'Warning 500', variable: '--warning-500', hex: '#f59e0b', usage: 'Warnings, caution states' },
    { name: 'Error 500', variable: '--error-500', hex: '#ef4444', usage: 'Errors, destructive actions' },
    { name: 'Gray 500', variable: '--gray-500', hex: '#6b7280', usage: 'Secondary text, muted content' },
    { name: 'Gray 700', variable: '--gray-700', hex: '#374151', usage: 'Primary text, headings' },
  ]

  const typographyExamples: TypographyExample[] = [
    { className: 'text-display-large', label: 'Display Large', example: 'Dashboard Overview' },
    { className: 'text-display-medium', label: 'Display Medium', example: 'Welcome Back!' },
    { className: 'text-heading-large', label: 'Heading Large', example: 'Sales Analytics' },
    { className: 'text-heading-medium', label: 'Heading Medium', example: 'Recent Activity' },
    { className: 'text-heading-small', label: 'Heading Small', example: 'Quick Actions' },
    { className: 'text-body-large', label: 'Body Large', example: 'This is the main body text for important content and descriptions.' },
    { className: 'text-body-medium', label: 'Body Medium', example: 'Standard body text for general content and information.' },
    { className: 'text-body-small', label: 'Body Small', example: 'Small text for captions and secondary information.' },
    { className: 'text-label-large', label: 'Label Large', example: 'Form Labels' },
    { className: 'text-label-medium', label: 'Label Medium', example: 'Button Text' },
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-display-large text-gray-900 mb-2">
            Caparan Tindahan Design System
          </h1>
          <p className="text-body-large text-muted">
            Professional design tokens, components, and guidelines for consistent UI development.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-white rounded-xl p-1 mb-8 shadow-sm border border-gray-200">
          {[
            { id: 'colors', label: 'Colors', icon: Palette },
            { id: 'typography', label: 'Typography', icon: Type },
            { id: 'components', label: 'Components', icon: Layout },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as 'colors' | 'typography' | 'components')}
              className={`
                flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-all
                ${activeTab === tab.id
                  ? 'bg-primary text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }
              `}
            >
              <tab.icon className="h-5 w-5" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Colors Tab */}
        {activeTab === 'colors' && (
          <div className="space-y-8">
            <div className="card-elevated p-8">
              <h2 className="text-heading-large text-gray-900 mb-6">Color Palette</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {colorPalette.map((color) => (
                  <div key={color.name} className="space-y-3">
                    <div 
                      className="h-20 rounded-lg shadow-sm border border-gray-200"
                      style={{ backgroundColor: color.hex }}
                    />
                    <div>
                      <h3 className="text-label-large text-gray-900">{color.name}</h3>
                      <p className="text-body-small text-muted font-mono">{color.hex}</p>
                      <p className="text-body-small text-subtle mt-1">{color.usage}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Color Usage Examples */}
            <div className="card-elevated p-8">
              <h2 className="text-heading-large text-gray-900 mb-6">Color Usage Examples</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-primary-light p-4 rounded-lg border border-green-200">
                  <Info className="h-6 w-6 text-primary mb-2" />
                  <p className="text-primary font-medium">Information</p>
                  <p className="text-body-small text-primary-dark">Primary color usage</p>
                </div>
                <div className="bg-success-light p-4 rounded-lg border border-green-200">
                  <CheckCircle className="h-6 w-6 text-success mb-2" />
                  <p className="text-success font-medium">Success</p>
                  <p className="text-body-small text-green-700">Success state</p>
                </div>
                <div className="bg-warning-light p-4 rounded-lg border border-yellow-200">
                  <AlertTriangle className="h-6 w-6 text-warning mb-2" />
                  <p className="text-warning font-medium">Warning</p>
                  <p className="text-body-small text-yellow-700">Warning state</p>
                </div>
                <div className="bg-error-light p-4 rounded-lg border border-red-200">
                  <XCircle className="h-6 w-6 text-error mb-2" />
                  <p className="text-error font-medium">Error</p>
                  <p className="text-body-small text-red-700">Error state</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Typography Tab */}
        {activeTab === 'typography' && (
          <div className="space-y-8">
            <div className="card-elevated p-8">
              <h2 className="text-heading-large text-gray-900 mb-6">Typography Scale</h2>
              <div className="space-y-6">
                {typographyExamples.map((example) => (
                  <div key={example.className} className="border-b border-gray-100 pb-6 last:border-b-0">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-label-large text-muted">{example.label}</span>
                      <code className="text-body-small bg-gray-100 px-2 py-1 rounded font-mono">
                        .{example.className}
                      </code>
                    </div>
                    <div className={`${example.className} text-gray-900`}>
                      {example.example}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Components Tab */}
        {activeTab === 'components' && (
          <div className="space-y-8">
            {/* Buttons */}
            <div className="card-elevated p-8">
              <h2 className="text-heading-large text-gray-900 mb-6">Buttons</h2>
              <div className="flex flex-wrap gap-4">
                <button className="btn-primary">
                  <Zap className="h-4 w-4" />
                  Primary Button
                </button>
                <button className="btn-success">
                  <CheckCircle className="h-4 w-4" />
                  Success Button
                </button>
                <button className="btn-secondary">
                  <Star className="h-4 w-4" />
                  Secondary Button
                </button>
                <button className="btn-outline-primary">
                  <Heart className="h-4 w-4" />
                  Outline Button
                </button>
                <button className="btn-primary" disabled>
                  Disabled Button
                </button>
              </div>

              {/* Theme Consistency Note */}
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-xl">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <h3 className="text-sm font-semibold text-green-900">Consistent Green Theme</h3>
                    <p className="text-sm text-green-700 mt-1">
                      All components now use the professional green theme for consistency across the application.
                      The primary color palette ensures a cohesive user experience.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Cards */}
            <div className="card-elevated p-8">
              <h2 className="text-heading-large text-gray-900 mb-6">Cards</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="card-elevated p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="bg-primary p-2 rounded-lg">
                      <Heart className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-heading-small text-gray-900">Elevated Card</h3>
                      <p className="text-body-small text-muted">With hover effects</p>
                    </div>
                  </div>
                  <p className="text-body-medium text-gray-700">
                    This card has elevation and smooth hover animations.
                  </p>
                </div>

                <div className="card-flat p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="bg-success p-2 rounded-lg">
                      <Bookmark className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="text-heading-small text-gray-900">Flat Card</h3>
                      <p className="text-body-small text-muted">Minimal design</p>
                    </div>
                  </div>
                  <p className="text-body-medium text-gray-700">
                    This card has a flat design with subtle hover effects.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
