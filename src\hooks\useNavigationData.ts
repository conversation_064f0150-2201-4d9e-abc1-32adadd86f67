'use client'

import { useState, useEffect } from 'react'

interface NavigationData {
  productsCount: number
  customersCount: number
  debtsCount: number
  paymentsCount: number
  loading: boolean
  lastUpdated?: Date
  recentItems?: {
    products: RecentItem[]
    customers: RecentItem[]
    payments: RecentItem[]
  }
}

interface RecentItem {
  id: string
  name: string
  href: string
  timestamp: string
}

interface DebtData {
  id: string
  isPaid: boolean
  totalAmount: number
  dateOfDebt: string
  customer?: {
    firstName: string
    lastName: string
  }
}

interface ProductData {
  id: string
  name: string
  updatedAt: string
}

interface CustomerData {
  id: string
  firstName: string
  lastName: string
  updatedAt: string
}

interface PaymentData {
  id: string
  amount: number
  updatedAt: string
  customer?: {
    firstName: string
    lastName: string
  }
}

export function useNavigationData(): NavigationData {
  const [data, setData] = useState<NavigationData>({
    productsCount: 0,
    customersCount: 0,
    debtsCount: 0,
    paymentsCount: 0,
    loading: true,
    recentItems: {
      products: [],
      customers: [],
      payments: []
    }
  })

  useEffect(() => {
    const fetchNavigationData = async () => {
      try {
        // Fetch data in parallel for better performance with timeout
        const fetchWithTimeout = (url: string, timeout = 5000) => {
          return Promise.race([
            fetch(url).then(res => res.ok ? res.json() : []),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Timeout')), timeout)
            )
          ])
        }

        const [productsRes, customersRes, debtsRes, paymentsRes] = await Promise.allSettled([
          fetchWithTimeout('/api/products'),
          fetchWithTimeout('/api/customers'),
          fetchWithTimeout('/api/debts'),
          fetchWithTimeout('/api/payments'),
        ])

        // Process counts
        const productsCount = productsRes.status === 'fulfilled' && Array.isArray(productsRes.value)
          ? productsRes.value.length : 0
        const customersCount = customersRes.status === 'fulfilled' && Array.isArray(customersRes.value)
          ? customersRes.value.length : 0
        const debtsCount = debtsRes.status === 'fulfilled' && Array.isArray(debtsRes.value)
          ? debtsRes.value.filter((debt: DebtData) => !debt.isPaid).length : 0
        const paymentsCount = paymentsRes.status === 'fulfilled' && Array.isArray(paymentsRes.value)
          ? paymentsRes.value.length : 0

        // Process recent items
        const recentProducts: RecentItem[] = productsRes.status === 'fulfilled' && Array.isArray(productsRes.value)
          ? productsRes.value
              .sort((a: ProductData, b: ProductData) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
              .slice(0, 5)
              .map((product: ProductData) => ({
                id: product.id,
                name: product.name,
                href: `/products/${product.id}`,
                timestamp: product.updatedAt
              }))
          : []

        const recentCustomers: RecentItem[] = customersRes.status === 'fulfilled' && Array.isArray(customersRes.value)
          ? customersRes.value
              .sort((a: CustomerData, b: CustomerData) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
              .slice(0, 5)
              .map((customer: CustomerData) => ({
                id: customer.id,
                name: `${customer.firstName} ${customer.lastName}`,
                href: `/customers/${customer.id}`,
                timestamp: customer.updatedAt
              }))
          : []

        const recentPayments: RecentItem[] = paymentsRes.status === 'fulfilled' && Array.isArray(paymentsRes.value)
          ? paymentsRes.value
              .sort((a: PaymentData, b: PaymentData) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
              .slice(0, 5)
              .map((payment: PaymentData) => ({
                id: payment.id,
                name: payment.customer ? `Payment from ${payment.customer.firstName} ${payment.customer.lastName}` : `Payment #${payment.id.slice(-6)}`,
                href: `/payments/${payment.id}`,
                timestamp: payment.updatedAt
              }))
          : []

        setData({
          productsCount,
          customersCount,
          debtsCount,
          paymentsCount,
          loading: false,
          lastUpdated: new Date(),
          recentItems: {
            products: recentProducts,
            customers: recentCustomers,
            payments: recentPayments
          }
        })
      } catch (error) {
        console.error('Error fetching navigation data:', error)
        // Set fallback data on error
        setData({
          productsCount: 0,
          customersCount: 0,
          debtsCount: 0,
          paymentsCount: 0,
          loading: false,
          lastUpdated: new Date(),
          recentItems: {
            products: [],
            customers: [],
            payments: []
          }
        })
      }
    }

    fetchNavigationData()

    // Refresh data every 5 minutes
    const interval = setInterval(fetchNavigationData, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [])

  return data
}
