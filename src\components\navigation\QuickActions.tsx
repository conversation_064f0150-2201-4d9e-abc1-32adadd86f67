'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Plus,
  Search,
  Download,
  Upload,
  MoreHorizontal,
  Zap,
  Clock
} from 'lucide-react'

interface QuickAction {
  label: string
  href?: string
  onClick?: () => void
  icon: React.ComponentType<{ className?: string }>
  description?: string
  shortcut?: string
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
}

interface QuickActionsProps {
  actions?: QuickAction[]
  showDefaults?: boolean
  className?: string
}

export default function QuickActions({
  actions = [],
  showDefaults = true,
  className = ""
}: QuickActionsProps) {
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  // Default actions based on current page
  const getDefaultActions = (): QuickAction[] => {
    const baseActions: QuickAction[] = [
      {
        label: 'Search',
        onClick: () => {
          // Focus search input
          const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement
          if (searchInput) {
            searchInput.focus()
          }
        },
        icon: Search,
        description: 'Search across all data',
        shortcut: 'Ctrl+K'
      }
    ]

    // Page-specific actions
    if (pathname.includes('/products')) {
      return [
        ...baseActions,
        {
          label: 'Add Product',
          href: '/products/new',
          icon: Plus,
          description: 'Add new product to inventory',
          variant: 'primary'
        },
        {
          label: 'Import Products',
          onClick: () => console.log('Import products'),
          icon: Upload,
          description: 'Bulk import from CSV'
        },
        {
          label: 'Export Products',
          onClick: () => console.log('Export products'),
          icon: Download,
          description: 'Download product data'
        }
      ]
    }

    if (pathname.includes('/customers')) {
      return [
        ...baseActions,
        {
          label: 'Add Customer',
          href: '/customers/new',
          icon: Plus,
          description: 'Register new customer',
          variant: 'primary'
        },
        {
          label: 'Customer Reports',
          href: '/reports/customers',
          icon: Download,
          description: 'Generate customer reports'
        }
      ]
    }

    if (pathname.includes('/debts')) {
      return [
        ...baseActions,
        {
          label: 'Record Debt',
          href: '/debts/new',
          icon: Plus,
          description: 'Record new customer debt',
          variant: 'warning'
        },
        {
          label: 'Payment Reminders',
          onClick: () => console.log('Send reminders'),
          icon: Clock,
          description: 'Send payment reminders'
        }
      ]
    }

    if (pathname.includes('/payments')) {
      return [
        ...baseActions,
        {
          label: 'Record Payment',
          href: '/payments/new',
          icon: Plus,
          description: 'Record new payment',
          variant: 'success'
        }
      ]
    }

    return baseActions
  }

  const allActions = showDefaults ? [...getDefaultActions(), ...actions] : actions

  const getVariantClasses = (variant?: string) => {
    switch (variant) {
      case 'primary':
        return 'bg-green-50 text-green-700 hover:bg-green-100 border-green-200'
      case 'success':
        return 'bg-green-50 text-green-700 hover:bg-green-100 border-green-200'
      case 'warning':
        return 'bg-yellow-50 text-yellow-700 hover:bg-yellow-100 border-yellow-200'
      case 'danger':
        return 'bg-red-50 text-red-700 hover:bg-red-100 border-red-200'
      default:
        return 'bg-gray-50 text-gray-700 hover:bg-gray-100 border-gray-200'
    }
  }

  if (allActions.length === 0) {
    return null
  }

  return (
    <div className={`relative ${className}`}>
      {/* Quick Actions Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-300 rounded-lg hover:border-gray-400 hover:shadow-sm transition-all duration-200"
        title="Quick Actions"
      >
        <Zap className="h-4 w-4 text-gray-600" />
        <span className="text-body-medium font-medium text-gray-700">Quick Actions</span>
        <MoreHorizontal className="h-4 w-4 text-gray-400" />
      </button>

      {/* Actions Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Actions Panel */}
          <div className="absolute top-full right-0 mt-2 w-80 bg-white border border-gray-200 rounded-xl shadow-xl z-50 animate-fade-in">
            {/* Header */}
            <div className="p-4 border-b border-gray-200">
              <h3 className="text-heading-small text-gray-900">Quick Actions</h3>
              <p className="text-body-small text-muted mt-1">
                Common actions for this page
              </p>
            </div>

            {/* Actions Grid */}
            <div className="p-4 space-y-2">
              {allActions.map((action, index) => {
                const IconComponent = action.icon
                const content = (
                  <div
                    className={`
                      group flex items-center space-x-3 p-3 rounded-lg border transition-all duration-200 cursor-pointer
                      ${getVariantClasses(action.variant)}
                    `}
                    onClick={() => {
                      if (action.onClick) {
                        action.onClick()
                      }
                      setIsOpen(false)
                    }}
                  >
                    <div className="flex-shrink-0">
                      <IconComponent className="h-5 w-5" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="text-body-medium font-medium">
                          {action.label}
                        </h4>
                        {action.shortcut && (
                          <span className="text-body-small text-gray-500 font-mono">
                            {action.shortcut}
                          </span>
                        )}
                      </div>
                      {action.description && (
                        <p className="text-body-small text-gray-600 mt-1">
                          {action.description}
                        </p>
                      )}
                    </div>
                  </div>
                )

                return action.href ? (
                  <Link key={index} href={action.href} onClick={() => setIsOpen(false)}>
                    {content}
                  </Link>
                ) : (
                  <div key={index}>
                    {content}
                  </div>
                )
              })}
            </div>

            {/* Footer */}
            <div className="p-3 border-t border-gray-200 bg-gray-50">
              <p className="text-body-small text-gray-500 text-center">
                Press <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">Ctrl+K</kbd> to search
              </p>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

// Floating Action Button for mobile
export function FloatingActionButton() {
  const pathname = usePathname()

  const getPrimaryAction = () => {
    if (pathname.includes('/products')) {
      return { label: 'Add Product', href: '/products/new', icon: Plus }
    }
    if (pathname.includes('/customers')) {
      return { label: 'Add Customer', href: '/customers/new', icon: Plus }
    }
    if (pathname.includes('/debts')) {
      return { label: 'Record Debt', href: '/debts/new', icon: Plus }
    }
    if (pathname.includes('/payments')) {
      return { label: 'Record Payment', href: '/payments/new', icon: Plus }
    }
    return null
  }

  const primaryAction = getPrimaryAction()

  if (!primaryAction) {
    return null
  }

  const IconComponent = primaryAction.icon

  return (
    <div className="fixed bottom-6 right-6 z-50 md:hidden">
      <Link
        href={primaryAction.href}
        className="flex items-center justify-center w-14 h-14 bg-primary text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
        title={primaryAction.label}
      >
        <IconComponent className="h-6 w-6" />
      </Link>
    </div>
  )
}

// Keyboard shortcuts component
export function KeyboardShortcuts() {
  const [isOpen, setIsOpen] = useState(false)

  const shortcuts = [
    { key: 'Ctrl+K', action: 'Search' },
    { key: 'Ctrl+N', action: 'New item' },
    { key: 'Ctrl+S', action: 'Save' },
    { key: 'Ctrl+E', action: 'Export' },
    { key: 'Ctrl+R', action: 'Refresh' },
    { key: 'Esc', action: 'Close dialogs' },
    { key: '?', action: 'Show shortcuts' }
  ]

  // Listen for keyboard shortcuts
  useState(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === '?' && !e.ctrlKey && !e.metaKey) {
        e.preventDefault()
        setIsOpen(true)
      }
      if (e.key === 'Escape') {
        setIsOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  })

  return (
    <>
      {/* Shortcuts Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => setIsOpen(false)}
          />
          
          {/* Modal */}
          <div className="relative bg-white rounded-xl shadow-xl p-6 max-w-md w-full mx-4 animate-scale-in">
            <h3 className="text-heading-large text-gray-900 mb-4">Keyboard Shortcuts</h3>
            <div className="space-y-3">
              {shortcuts.map((shortcut, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-body-medium text-gray-700">{shortcut.action}</span>
                  <kbd className="px-2 py-1 bg-gray-100 rounded text-body-small font-mono">
                    {shortcut.key}
                  </kbd>
                </div>
              ))}
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="mt-6 w-full btn-secondary"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </>
  )
}
