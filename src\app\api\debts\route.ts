import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { customerDebtSchema } from '@/lib/validations'

// GET /api/debts - Get all customer debts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')
    const isPaid = searchParams.get('isPaid')

    const where: { customerId?: string; isPaid?: boolean } = {}
    if (customerId) where.customerId = customerId
    if (isPaid !== null) where.isPaid = isPaid === 'true'

    const debts = await prisma.customerDebt.findMany({
      where,
      include: {
        customer: true,
        product: true,
        payments: true,
      },
      orderBy: { dateOfDebt: 'desc' },
    })

    return NextResponse.json(debts)
  } catch (error) {
    console.error('Error fetching debts:', error)
    // Return empty array instead of error to prevent dashboard crashes
    return NextResponse.json([])
  }
}

// POST /api/debts - Create a new customer debt
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = customerDebtSchema.parse(body)

    // Get product details to store current price and name
    const product = await prisma.product.findUnique({
      where: { id: validatedData.productId },
    })

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Check if there's enough stock
    if (product.stock < validatedData.quantity) {
      return NextResponse.json(
        { error: 'Insufficient stock' },
        { status: 400 }
      )
    }

    const totalAmount = product.price * validatedData.quantity

    // Create the debt and update product stock in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the debt
      const debt = await tx.customerDebt.create({
        data: {
          customerId: validatedData.customerId,
          productId: validatedData.productId,
          productName: product.name,
          productPrice: product.price,
          quantity: validatedData.quantity,
          totalAmount,
          dateOfDebt: validatedData.dateOfDebt || new Date(),
        },
        include: {
          customer: true,
          product: true,
        },
      })

      // Update product stock
      await tx.product.update({
        where: { id: validatedData.productId },
        data: {
          stock: {
            decrement: validatedData.quantity,
          },
        },
      })

      return debt
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating debt:', error)
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid debt data', details: error },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create debt' },
      { status: 500 }
    )
  }
}
