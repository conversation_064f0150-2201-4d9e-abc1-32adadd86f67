'use client'

import { useState, useEffect } from 'react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  text?: string
  showText?: boolean
  className?: string
}

export function LoadingSpinner({
  size = 'md',
  variant = 'primary',
  text = 'Loading...',
  showText = true,
  className = ''
}: LoadingSpinnerProps) {
  const [dots, setDots] = useState('')

  useEffect(() => {
    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return ''
        return prev + '.'
      })
    }, 500)

    return () => clearInterval(interval)
  }, [])

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colorClasses = {
    primary: 'border-primary-600',
    secondary: 'border-gray-600',
    success: 'border-success-600',
    warning: 'border-warning-600',
    error: 'border-error-600'
  }

  const textColorClasses = {
    primary: 'text-primary-600',
    secondary: 'text-gray-600',
    success: 'text-success-600',
    warning: 'text-warning-600',
    error: 'text-error-600'
  }

  return (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      {/* Animated Spinner */}
      <div className="relative">
        <div 
          className={`
            ${sizeClasses[size]} 
            border-2 border-gray-200 rounded-full
            animate-spin
          `}
        />
        <div 
          className={`
            absolute inset-0
            ${sizeClasses[size]} 
            border-2 ${colorClasses[variant]} border-t-transparent rounded-full
            animate-spin
          `}
        />
        
        {/* Pulsing Center Dot */}
        <div 
          className={`
            absolute inset-0 m-auto
            ${size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-1.5 h-1.5' : size === 'lg' ? 'w-2 h-2' : 'w-3 h-3'}
            ${colorClasses[variant].replace('border-', 'bg-')} rounded-full
            animate-pulse-gentle
          `}
        />
      </div>

      {/* Loading Text */}
      {showText && (
        <div className={`text-center ${textColorClasses[variant]}`}>
          <p className={`font-medium ${size === 'sm' ? 'text-xs' : size === 'md' ? 'text-sm' : 'text-base'}`}>
            {text}{dots}
          </p>
        </div>
      )}
    </div>
  )
}

// Skeleton Loading Component
interface SkeletonProps {
  className?: string
  variant?: 'text' | 'circular' | 'rectangular'
  width?: string | number
  height?: string | number
  animate?: boolean
}

export function Skeleton({
  className = '',
  variant = 'rectangular',
  width,
  height,
  animate = true
}: SkeletonProps) {
  const baseClasses = `bg-gray-200 ${animate ? 'loading-shimmer' : ''}`
  
  const variantClasses = {
    text: 'rounded',
    circular: 'rounded-full',
    rectangular: 'rounded-lg'
  }

  const style: React.CSSProperties = {}
  if (width) style.width = typeof width === 'number' ? `${width}px` : width
  if (height) style.height = typeof height === 'number' ? `${height}px` : height

  return (
    <div 
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      style={style}
    />
  )
}

// Page Loading Component
export function PageLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <LoadingSpinner size="xl" text="Loading Dashboard" />
        <div className="mt-8 space-y-4 max-w-md">
          <Skeleton height={20} className="w-3/4 mx-auto" />
          <Skeleton height={16} className="w-1/2 mx-auto" />
          <div className="grid grid-cols-3 gap-4 mt-6">
            <Skeleton height={60} />
            <Skeleton height={60} />
            <Skeleton height={60} />
          </div>
        </div>
      </div>
    </div>
  )
}

// Button Loading State
interface LoadingButtonProps {
  loading?: boolean
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary'
  disabled?: boolean
  className?: string
}

export function LoadingButton({
  loading = false,
  children,
  onClick,
  variant = 'primary',
  disabled = false,
  className = ''
}: LoadingButtonProps) {
  const baseClass = variant === 'primary' ? 'btn-primary' : 'btn-secondary'
  
  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClass} btn-interactive
        ${loading ? 'cursor-not-allowed opacity-75' : ''}
        ${className}
      `}
    >
      {loading && (
        <LoadingSpinner 
          size="sm" 
          variant={variant} 
          showText={false}
          className="mr-2"
        />
      )}
      {children}
    </button>
  )
}

export default LoadingSpinner
