'use client'

import { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts'
import { TrendingUp, TrendingDown, BarChart3 } from 'lucide-react'

interface SalesData {
  date: string
  sales: number
  target: number
  revenue: number
}

interface SalesChartProps {
  data: SalesData[]
  title?: string
  height?: number
  showComparison?: boolean
  animated?: boolean
}

export default function SalesChart({
  data,
  title = "Sales Performance",
  height = 400,
  showComparison = true,
  animated = true
}: SalesChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Calculate metrics
  const totalSales = data.reduce((sum, item) => sum + item.sales, 0)
  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0)
  const avgTarget = data.reduce((sum, item) => sum + item.target, 0) / data.length
  const avgSales = totalSales / data.length
  const performanceRate = ((avgSales / avgTarget) * 100).toFixed(1)
  const isPositive = avgSales >= avgTarget

  useEffect(() => {
    if (!chartRef.current || !data.length) return

    // Initialize chart
    chartInstance.current = echarts.init(chartRef.current, 'light', {
      renderer: 'canvas',
      useDirtyRect: false
    })

    const option: echarts.EChartsOption = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 18,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formatter: function (params: any) {
          const data = params[0]
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const salesData = params.find((p: any) => p.seriesName === 'Sales')
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const targetData = params.find((p: any) => p.seriesName === 'Target')
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const revenueData = params.find((p: any) => p.seriesName === 'Revenue')
          
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 8px;">${data.axisValue}</div>
              ${salesData ? `<div style="margin: 4px 0;"><span style="color: #3b82f6;">●</span> Sales: ${salesData.value}%</div>` : ''}
              ${targetData ? `<div style="margin: 4px 0;"><span style="color: #ef4444;">●</span> Target: ${targetData.value}%</div>` : ''}
              ${revenueData ? `<div style="margin: 4px 0;"><span style="color: #10b981;">●</span> Revenue: ₱${revenueData.value.toLocaleString()}</div>` : ''}
            </div>
          `
        }
      },
      legend: {
        data: ['Sales', 'Target', 'Revenue'],
        top: 40,
        textStyle: {
          color: '#6b7280'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: [
        {
          type: 'category',
          data: data.map(item => item.date),
          axisPointer: {
            type: 'shadow'
          },
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          axisLabel: {
            color: '#6b7280'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: 'Performance (%)',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          axisLabel: {
            color: '#6b7280',
            formatter: '{value}%'
          },
          splitLine: {
            lineStyle: {
              color: '#f3f4f6'
            }
          }
        },
        {
          type: 'value',
          name: 'Revenue (₱)',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: '#e5e7eb'
            }
          },
          axisLabel: {
            color: '#6b7280',
            formatter: '₱{value}'
          }
        }
      ],
      series: [
        {
          name: 'Sales',
          type: 'bar',
          data: data.map(item => item.sales),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#60a5fa' },
              { offset: 1, color: '#3b82f6' }
            ]),
            borderRadius: [4, 4, 0, 0]
          },
          emphasis: {
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#93c5fd' },
                { offset: 1, color: '#60a5fa' }
              ])
            }
          },
          animationDelay: animated ? (idx: number) => idx * 100 : 0
        },
        {
          name: 'Target',
          type: 'line',
          data: data.map(item => item.target),
          lineStyle: {
            color: '#ef4444',
            width: 3
          },
          itemStyle: {
            color: '#ef4444'
          },
          symbol: 'circle',
          symbolSize: 6,
          animationDelay: animated ? (idx: number) => idx * 100 + 200 : 0
        },
        {
          name: 'Revenue',
          type: 'line',
          yAxisIndex: 1,
          data: data.map(item => item.revenue),
          lineStyle: {
            color: '#10b981',
            width: 3
          },
          itemStyle: {
            color: '#10b981'
          },
          symbol: 'diamond',
          symbolSize: 8,
          animationDelay: animated ? (idx: number) => idx * 100 + 400 : 0
        }
      ],
      animation: animated,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    chartInstance.current.setOption(option)
    
    // Loading simulation
    setTimeout(() => setIsLoading(false), 800)

    // Handle resize
    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, height, animated])

  return (
    <div className="card-elevated p-6 hover-lift animate-fade-in">
      {/* Chart Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg">
            <BarChart3 className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-heading-medium text-gray-900">{title}</h3>
            <p className="text-body-small text-muted">Performance vs targets</p>
          </div>
        </div>
        
        {showComparison && (
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="flex items-center space-x-1">
                {isPositive ? (
                  <TrendingUp className="h-4 w-4 text-success" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-error" />
                )}
                <span className={`text-sm font-semibold ${isPositive ? 'text-success' : 'text-error'}`}>
                  {performanceRate}%
                </span>
              </div>
              <p className="text-body-small text-muted">vs target</p>
            </div>
          </div>
        )}
      </div>

      {/* Chart Container */}
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-body-medium text-muted">Loading chart...</span>
            </div>
          </div>
        )}
        <div
          ref={chartRef}
          style={{ height: `${height}px`, width: '100%' }}
          className="transition-opacity duration-300"
        />
      </div>

      {/* Chart Summary */}
      <div className="mt-6 grid grid-cols-3 gap-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <p className="text-body-small text-muted">Total Sales</p>
          <p className="text-heading-small text-gray-900">{totalSales.toFixed(1)}%</p>
        </div>
        <div className="text-center">
          <p className="text-body-small text-muted">Revenue</p>
          <p className="text-heading-small text-gray-900">₱{totalRevenue.toLocaleString()}</p>
        </div>
        <div className="text-center">
          <p className="text-body-small text-muted">Avg Performance</p>
          <p className={`text-heading-small ${isPositive ? 'text-success' : 'text-error'}`}>
            {performanceRate}%
          </p>
        </div>
      </div>
    </div>
  )
}
