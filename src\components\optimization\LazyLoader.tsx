'use client'

import { Suspense, lazy, ComponentType, ReactNode, useState, useEffect, useRef } from 'react'
import Image from 'next/image'
import { LoadingSpinner } from '../LoadingSpinner'

interface LazyLoaderProps {
  children: ReactNode
  fallback?: ReactNode
  className?: string
}

// Generic lazy loader component
export function LazyLoader({ children, fallback, className = "" }: LazyLoaderProps) {
  const defaultFallback = (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <LoadingSpinner size="lg" text="Loading component..." />
    </div>
  )

  return (
    <Suspense fallback={fallback || defaultFallback}>
      {children}
    </Suspense>
  )
}

// Higher-order component for lazy loading
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function withLazyLoading<T = any>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  fallback?: ReactNode
) {
  const LazyComponent = lazy(importFunc)

  return function LazyWrappedComponent(props: T) {
    return (
      <LazyLoader fallback={fallback}>
        {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
        <LazyComponent {...(props as any)} />
      </LazyLoader>
    )
  }
}

// Lazy load charts with specific loading state
export const LazyCharts = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  SalesChart: withLazyLoading<any>(
    () => import('../charts/SalesChart'),
    <div className="card-elevated p-6 animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  ),

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  RevenueChart: withLazyLoading<any>(
    () => import('../charts/RevenueChart'),
    <div className="card-elevated p-6 animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  ),

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  InventoryChart: withLazyLoading<any>(
    () => import('../charts/InventoryChart'),
    <div className="card-elevated p-6 animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  ),

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  CustomerChart: withLazyLoading<any>(
    () => import('../charts/CustomerChart'),
    <div className="card-elevated p-6 animate-pulse">
      <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
      <div className="h-64 bg-gray-200 rounded"></div>
    </div>
  )
}

// Lazy load search components
export const LazySearch = {
  SmartSearch: withLazyLoading(
    () => import('../search/SmartSearch'),
    <div className="animate-pulse">
      <div className="h-12 bg-gray-200 rounded-xl"></div>
    </div>
  ),

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  DataTable: withLazyLoading<any>(
    () => import('../search/DataTable'),
    <div className="card-elevated p-6 animate-pulse">
      <div className="space-y-4">
        <div className="h-12 bg-gray-200 rounded"></div>
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded"></div>
        ))}
      </div>
    </div>
  ),

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  AdvancedFilter: withLazyLoading<any>(
    () => import('../search/AdvancedFilter'),
    <div className="animate-pulse">
      <div className="h-10 bg-gray-200 rounded w-24"></div>
    </div>
  )
}

// Intersection Observer for lazy loading on scroll
export function useIntersectionObserver(
  ref: React.RefObject<Element | null>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [ref, options])

  return isIntersecting
}

// Lazy load component when it enters viewport
interface LazyOnScrollProps {
  children: ReactNode
  fallback?: ReactNode
  className?: string
  threshold?: number
  rootMargin?: string
}

export function LazyOnScroll({
  children,
  fallback,
  className = "",
  threshold = 0.1,
  rootMargin = '50px'
}: LazyOnScrollProps) {
  const ref = useRef<HTMLDivElement>(null)
  const isVisible = useIntersectionObserver(ref, { threshold, rootMargin })
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    if (isVisible && !hasLoaded) {
      setHasLoaded(true)
    }
  }, [isVisible, hasLoaded])

  return (
    <div ref={ref} className={className}>
      {hasLoaded ? children : (fallback || <div className="h-64 bg-gray-100 rounded animate-pulse" />)}
    </div>
  )
}

// Progressive image loading
interface ProgressiveImageProps {
  src: string
  alt: string
  placeholder?: string
  className?: string
  width?: number
  height?: number
}

export function ProgressiveImage({
  src,
  alt,
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PC9zdmc+',
  className = "",
  width,
  height
}: ProgressiveImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Placeholder */}
      {!isLoaded && !isError && (
        <Image
          src={placeholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover filter blur-sm"
          width={width}
          height={height}
        />
      )}
      
      {/* Main image */}
      <Image
        src={src}
        alt={alt}
        className={`
          w-full h-full object-cover transition-opacity duration-300
          ${isLoaded ? 'opacity-100' : 'opacity-0'}
        `}
        onLoad={() => setIsLoaded(true)}
        onError={() => setIsError(true)}
        width={width}
        height={height}
        loading="lazy"
      />
      
      {/* Error state */}
      {isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <div className="w-12 h-12 bg-gray-300 rounded-full mx-auto mb-2"></div>
            <p className="text-sm text-gray-500">Failed to load image</p>
          </div>
        </div>
      )}
      
      {/* Loading indicator */}
      {!isLoaded && !isError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  )
}

// Bundle analyzer helper (development only)
export function BundleAnalyzer() {
  const [bundleInfo, setBundleInfo] = useState<{
    totalSize: string;
    gzippedSize: string;
    chunks: Array<{ name: string; size: string; gzipped: string }>;
  } | null>(null)

  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') {
      return
    }
    // Simulate bundle analysis
    const info = {
      totalSize: '2.1 MB',
      gzippedSize: '650 KB',
      chunks: [
        { name: 'main', size: '1.2 MB', gzipped: '380 KB' },
        { name: 'vendor', size: '650 KB', gzipped: '200 KB' },
        { name: 'charts', size: '250 KB', gzipped: '70 KB' }
      ]
    }
    setBundleInfo(info)
  }, [])

  if (process.env.NODE_ENV !== 'development' || !bundleInfo) {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 bg-black text-white p-4 rounded-lg text-xs font-mono z-50">
      <div className="mb-2 font-bold">Bundle Info (Dev)</div>
      <div>Total: {bundleInfo.totalSize} ({bundleInfo.gzippedSize} gzipped)</div>
      {bundleInfo.chunks.map((chunk: { name: string; size: string; gzipped: string }, i: number) => (
        <div key={i} className="text-gray-300">
          {chunk.name}: {chunk.size} ({chunk.gzipped})
        </div>
      ))}
    </div>
  )
}


