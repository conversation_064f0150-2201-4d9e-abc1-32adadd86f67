import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { customerSchema } from '@/lib/validations'

// GET /api/customers - Get all customers
export async function GET() {
  try {
    const customers = await prisma.customer.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        debts: {
          where: { isPaid: false },
          include: {
            product: true,
          },
        },
        payments: true,
        _count: {
          select: {
            debts: true,
            payments: true,
          },
        },
      },
    })

    // Calculate total debt for each customer
    const customersWithTotalDebt = customers.map(customer => {
      const totalDebt = customer.debts.reduce((sum, debt) => sum + debt.totalAmount, 0)
      const totalPaid = customer.payments.reduce((sum, payment) => sum + payment.amount, 0)
      const remainingDebt = totalDebt - totalPaid

      return {
        ...customer,
        totalDebt,
        totalPaid,
        remainingDebt,
      }
    })

    // If no customers found, return sample data for demo purposes
    if (customersWithTotalDebt.length === 0) {
      const sampleCustomers = [
        {
          id: 'sample-customer-1',
          firstName: 'Juan',
          lastName: 'Dela Cruz',
          email: '<EMAIL>',
          phone: '09123456789',
          address: '123 Main St, Manila',
          debts: [],
          payments: [],
          _count: { debts: 0, payments: 0 },
          totalDebt: 0,
          totalPaid: 0,
          remainingDebt: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'sample-customer-2',
          firstName: 'Maria',
          lastName: 'Santos',
          email: '<EMAIL>',
          phone: '09987654321',
          address: '456 Oak Ave, Quezon City',
          debts: [],
          payments: [],
          _count: { debts: 0, payments: 0 },
          totalDebt: 0,
          totalPaid: 0,
          remainingDebt: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      ]
      return NextResponse.json(sampleCustomers)
    }

    return NextResponse.json(customersWithTotalDebt)
  } catch (error) {
    console.error('Error fetching customers:', error)
    // Return empty array instead of error to prevent dashboard crashes
    return NextResponse.json([])
  }
}

// POST /api/customers - Create a new customer
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = customerSchema.parse(body)

    const customer = await prisma.customer.create({
      data: validatedData,
    })

    return NextResponse.json(customer, { status: 201 })
  } catch (error) {
    console.error('Error creating customer:', error)
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid customer data', details: error },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create customer' },
      { status: 500 }
    )
  }
}
