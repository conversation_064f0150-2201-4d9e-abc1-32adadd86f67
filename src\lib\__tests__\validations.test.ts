import { 
  productSchema, 
  customerSchema, 
  customerDebtSchema, 
  paymentSchema 
} from '../validations'

describe('Validation Schemas', () => {
  describe('productSchema', () => {
    it('should validate a valid product', () => {
      const validProduct = {
        name: 'Test Product',
        netWeight: '100g',
        price: 25.50,
        stock: 10,
        category: 'Snacks',
        imageUrl: 'https://example.com/image.jpg'
      }

      const result = productSchema.safeParse(validProduct)
      expect(result.success).toBe(true)
    })

    it('should validate a product without imageUrl', () => {
      const validProduct = {
        name: 'Test Product',
        netWeight: '100g',
        price: 25.50,
        stock: 10,
        category: 'Snacks'
      }

      const result = productSchema.safeParse(validProduct)
      expect(result.success).toBe(true)
    })

    it('should reject product with empty name', () => {
      const invalidProduct = {
        name: '',
        netWeight: '100g',
        price: 25.50,
        stock: 10,
        category: 'Snacks'
      }

      const result = productSchema.safeParse(invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Product name is required')
      }
    })

    it('should reject product with negative price', () => {
      const invalidProduct = {
        name: 'Test Product',
        netWeight: '100g',
        price: -5,
        stock: 10,
        category: 'Snacks'
      }

      const result = productSchema.safeParse(invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Price must be greater than 0')
      }
    })

    it('should reject product with negative stock', () => {
      const invalidProduct = {
        name: 'Test Product',
        netWeight: '100g',
        price: 25.50,
        stock: -1,
        category: 'Snacks'
      }

      const result = productSchema.safeParse(invalidProduct)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Stock must be 0 or greater')
      }
    })
  })

  describe('customerSchema', () => {
    it('should validate a valid customer', () => {
      const validCustomer = {
        firstName: 'John',
        lastName: 'Doe'
      }

      const result = customerSchema.safeParse(validCustomer)
      expect(result.success).toBe(true)
    })

    it('should reject customer with empty firstName', () => {
      const invalidCustomer = {
        firstName: '',
        lastName: 'Doe'
      }

      const result = customerSchema.safeParse(invalidCustomer)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('First name is required')
      }
    })

    it('should reject customer with empty lastName', () => {
      const invalidCustomer = {
        firstName: 'John',
        lastName: ''
      }

      const result = customerSchema.safeParse(invalidCustomer)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Last name is required')
      }
    })
  })

  describe('customerDebtSchema', () => {
    it('should validate a valid customer debt', () => {
      const validDebt = {
        customerId: 'customer-123',
        productId: 'product-456',
        quantity: 2,
        dateOfDebt: new Date()
      }

      const result = customerDebtSchema.safeParse(validDebt)
      expect(result.success).toBe(true)
    })

    it('should validate debt without dateOfDebt', () => {
      const validDebt = {
        customerId: 'customer-123',
        productId: 'product-456',
        quantity: 2
      }

      const result = customerDebtSchema.safeParse(validDebt)
      expect(result.success).toBe(true)
    })

    it('should reject debt with zero quantity', () => {
      const invalidDebt = {
        customerId: 'customer-123',
        productId: 'product-456',
        quantity: 0
      }

      const result = customerDebtSchema.safeParse(invalidDebt)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Quantity must be at least 1')
      }
    })
  })

  describe('paymentSchema', () => {
    it('should validate a valid payment', () => {
      const validPayment = {
        customerId: 'customer-123',
        customerDebtId: 'debt-456',
        amount: 100.50,
        notes: 'Payment for groceries',
        dateOfPayment: new Date()
      }

      const result = paymentSchema.safeParse(validPayment)
      expect(result.success).toBe(true)
    })

    it('should validate payment without optional fields', () => {
      const validPayment = {
        customerId: 'customer-123',
        amount: 100.50
      }

      const result = paymentSchema.safeParse(validPayment)
      expect(result.success).toBe(true)
    })

    it('should reject payment with zero amount', () => {
      const invalidPayment = {
        customerId: 'customer-123',
        amount: 0
      }

      const result = paymentSchema.safeParse(invalidPayment)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].message).toBe('Amount must be greater than 0')
      }
    })
  })
})
