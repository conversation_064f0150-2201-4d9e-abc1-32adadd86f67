// Advanced caching system for optimal performance

interface CacheItem<T> {
  data: T
  timestamp: number
  ttl: number
  key: string
}

interface CacheOptions {
  ttl?: number // Time to live in milliseconds
  maxSize?: number // Maximum number of items
  storage?: 'memory' | 'localStorage' | 'sessionStorage'
}

class AdvancedCache<T> {
  private cache = new Map<string, CacheItem<T>>()
  private maxSize: number
  private defaultTTL: number
  private storage: 'memory' | 'localStorage' | 'sessionStorage'

  constructor(options: CacheOptions = {}) {
    this.maxSize = options.maxSize || 100
    this.defaultTTL = options.ttl || 5 * 60 * 1000 // 5 minutes
    this.storage = options.storage || 'memory'
    
    // Load from persistent storage if available
    if (this.storage !== 'memory' && typeof window !== 'undefined') {
      this.loadFromStorage()
    }
  }

  set(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      key
    }

    // Remove oldest item if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value
      if (oldestKey !== undefined) {
        this.cache.delete(oldestKey)
      }
    }

    this.cache.set(key, item)
    this.saveToStorage()
  }

  get(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key)
      this.saveToStorage()
      return null
    }

    return item.data
  }

  has(key: string): boolean {
    return this.get(key) !== null
  }

  delete(key: string): boolean {
    const result = this.cache.delete(key)
    this.saveToStorage()
    return result
  }

  clear(): void {
    this.cache.clear()
    this.saveToStorage()
  }

  size(): number {
    return this.cache.size
  }

  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  // Get cache statistics
  getStats() {
    const now = Date.now()
    const items = Array.from(this.cache.values())
    const expired = items.filter(item => now - item.timestamp > item.ttl).length
    const valid = items.length - expired

    return {
      total: items.length,
      valid,
      expired,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0,
      memoryUsage: this.estimateMemoryUsage()
    }
  }

  private hitCount = 0
  private missCount = 0

  private estimateMemoryUsage(): number {
    let size = 0
    for (const item of this.cache.values()) {
      size += JSON.stringify(item).length * 2 // Rough estimate
    }
    return size
  }

  private loadFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const storage = this.storage === 'localStorage' ? localStorage : sessionStorage
      const data = storage.getItem(`cache_${this.constructor.name}`)
      
      if (data) {
        const parsed = JSON.parse(data)
        this.cache = new Map(parsed)
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error)
    }
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined' || this.storage === 'memory') return

    try {
      const storage = this.storage === 'localStorage' ? localStorage : sessionStorage
      const data = JSON.stringify(Array.from(this.cache.entries()))
      storage.setItem(`cache_${this.constructor.name}`, data)
    } catch (error) {
      console.warn('Failed to save cache to storage:', error)
    }
  }
}

// Specialized caches for different data types
export const apiCache = new AdvancedCache<unknown>({
  ttl: 5 * 60 * 1000, // 5 minutes
  maxSize: 50,
  storage: 'sessionStorage'
})

export const imageCache = new AdvancedCache<string>({
  ttl: 30 * 60 * 1000, // 30 minutes
  maxSize: 100,
  storage: 'localStorage'
})

export const userDataCache = new AdvancedCache<unknown>({
  ttl: 10 * 60 * 1000, // 10 minutes
  maxSize: 20,
  storage: 'localStorage'
})

// Memoization decorator for expensive functions
export function memoize<T extends (...args: unknown[]) => unknown>(
  fn: T,
  options: { ttl?: number; maxSize?: number } = {}
): T {
  const cache = new AdvancedCache<ReturnType<T>>(options)

  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args)
    const cached = cache.get(key)
    
    if (cached !== null) {
      return cached
    }

    const result = fn(...args) as ReturnType<T>
    cache.set(key, result)
    return result
  }) as T
}

// Debounced function cache
export function debounce<T extends (...args: unknown[]) => unknown>(
  fn: T,
  delay: number
): T {
  let timeoutId: NodeJS.Timeout
  
  return ((...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => fn(...args), delay)
  }) as T
}

// Throttled function cache
export function throttle<T extends (...args: unknown[]) => unknown>(
  fn: T,
  limit: number
): T {
  let inThrottle: boolean
  
  return ((...args: Parameters<T>) => {
    if (!inThrottle) {
      fn(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }) as T
}

// Request deduplication
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<unknown>>()

  async dedupe<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)! as Promise<T>
    }

    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key)
    })

    this.pendingRequests.set(key, promise)
    return promise
  }
}

export const requestDeduplicator = new RequestDeduplicator()

// Optimized fetch with caching and deduplication
export async function optimizedFetch<T>(
  url: string,
  options: RequestInit & { 
    cache?: boolean
    cacheTTL?: number
    dedupe?: boolean
  } = {}
): Promise<T> {
  const { cache = true, cacheTTL, dedupe = true, ...fetchOptions } = options
  const cacheKey = `${url}_${JSON.stringify(fetchOptions)}`

  // Check cache first
  if (cache) {
    const cached = apiCache.get(cacheKey)
    if (cached) {
      return cached as T
    }
  }

  // Deduplicate requests
  const fetchFn = async () => {
    const response = await fetch(url, fetchOptions)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    // Cache successful responses
    if (cache) {
      apiCache.set(cacheKey, data, cacheTTL)
    }
    
    return data
  }

  if (dedupe) {
    return requestDeduplicator.dedupe(cacheKey, fetchFn)
  }

  return fetchFn()
}

// Batch API requests
export class BatchRequestManager {
  private batches = new Map<string, {
    requests: Array<{ resolve: (value: unknown) => void; reject: (reason?: unknown) => void; params: unknown }>
    timeout: NodeJS.Timeout
  }>()

  batch<T>(
    batchKey: string,
    params: unknown,
    batchFn: (requests: unknown[]) => Promise<T[]>,
    delay: number = 50
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.batches.has(batchKey)) {
        this.batches.set(batchKey, {
          requests: [],
          timeout: setTimeout(() => this.executeBatch(batchKey, batchFn), delay)
        })
      }

      const batch = this.batches.get(batchKey)!
      batch.requests.push({ resolve: resolve as (value: unknown) => void, reject, params })
    })
  }

  private async executeBatch<T>(
    batchKey: string,
    batchFn: (requests: unknown[]) => Promise<T[]>
  ) {
    const batch = this.batches.get(batchKey)
    if (!batch) return

    this.batches.delete(batchKey)

    try {
      const params = batch.requests.map(req => req.params)
      const results = await batchFn(params)
      
      batch.requests.forEach((req, index) => {
        req.resolve(results[index])
      })
    } catch (error) {
      batch.requests.forEach(req => {
        req.reject(error)
      })
    }
  }
}

export const batchRequestManager = new BatchRequestManager()

// Performance monitoring for cache
export function getCachePerformanceReport() {
  return {
    api: apiCache.getStats(),
    image: imageCache.getStats(),
    userData: userDataCache.getStats(),
    timestamp: new Date().toISOString()
  }
}

// Cache warming utilities
export async function warmCache(urls: string[]) {
  const promises = urls.map(url => 
    optimizedFetch(url).catch(error => 
      console.warn(`Failed to warm cache for ${url}:`, error)
    )
  )
  
  await Promise.allSettled(promises)
}

// Preload critical resources
export function preloadCriticalResources() {
  if (typeof window === 'undefined') return

  // Preload critical API endpoints
  const criticalEndpoints = [
    '/api/dashboard/stats',
    '/api/products?limit=10',
    '/api/customers?limit=10'
  ]

  warmCache(criticalEndpoints)

  // Preload critical images
  const criticalImages = [
    '/images/logo.png',
    '/images/placeholder.jpg'
  ]

  criticalImages.forEach(src => {
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'image'
    link.href = src
    document.head.appendChild(link)
  })
}
