const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function main() {
  // Create sample products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'Lucky Me Instant Noodles',
        netWeight: '70g',
        price: 15.00,
        stock: 50,
        category: 'Noodles',
      },
    }),
    prisma.product.create({
      data: {
        name: 'Coca-Cola 350ml',
        netWeight: '350ml',
        price: 25.00,
        stock: 30,
        category: 'Beverages',
      },
    }),
    prisma.product.create({
      data: {
        name: 'Skyflakes Crackers',
        netWeight: '250g',
        price: 35.00,
        stock: 20,
        category: 'Snacks',
      },
    }),
    prisma.product.create({
      data: {
        name: 'Corned Beef',
        netWeight: '150g',
        price: 45.00,
        stock: 15,
        category: 'Canned Goods',
      },
    }),
    prisma.product.create({
      data: {
        name: 'Rice 1kg',
        netWeight: '1kg',
        price: 55.00,
        stock: 25,
        category: 'Rice & Grains',
      },
    }),
  ])

  // Create sample customers
  const customers = await Promise.all([
    prisma.customer.create({
      data: {
        firstName: 'Maria',
        lastName: '<PERSON>',
      },
    }),
    prisma.customer.create({
      data: {
        firstName: 'Juan',
        lastName: 'Dela Cruz',
      },
    }),
    prisma.customer.create({
      data: {
        firstName: 'Ana',
        lastName: 'Garcia',
      },
    }),
  ])

  // Create sample debts
  await Promise.all([
    prisma.customerDebt.create({
      data: {
        customerId: customers[0].id,
        productId: products[0].id,
        productName: products[0].name,
        productPrice: products[0].price,
        quantity: 3,
        totalAmount: products[0].price * 3,
        dateOfDebt: new Date('2024-01-15'),
      },
    }),
    prisma.customerDebt.create({
      data: {
        customerId: customers[1].id,
        productId: products[1].id,
        productName: products[1].name,
        productPrice: products[1].price,
        quantity: 2,
        totalAmount: products[1].price * 2,
        dateOfDebt: new Date('2024-01-20'),
      },
    }),
    prisma.customerDebt.create({
      data: {
        customerId: customers[0].id,
        productId: products[2].id,
        productName: products[2].name,
        productPrice: products[2].price,
        quantity: 1,
        totalAmount: products[2].price * 1,
        dateOfDebt: new Date('2024-01-25'),
        isPaid: true,
      },
    }),
  ])

  // Create sample payments
  await Promise.all([
    prisma.payment.create({
      data: {
        customerId: customers[0].id,
        amount: 35.00,
        dateOfPayment: new Date('2024-01-26'),
        notes: 'Payment for crackers',
      },
    }),
    prisma.payment.create({
      data: {
        customerId: customers[1].id,
        amount: 25.00,
        dateOfPayment: new Date('2024-01-22'),
        notes: 'Partial payment',
      },
    }),
  ])

  console.log('Seed data created successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
