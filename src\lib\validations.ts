import { z } from 'zod'

// Product validation schema
export const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  imageUrl: z.string().optional(),
  netWeight: z.string().min(1, 'Net weight is required'),
  price: z.number().min(0.01, 'Price must be greater than 0'),
  stock: z.number().int().min(0, 'Stock must be 0 or greater'),
  category: z.string().min(1, 'Category is required'),
})

export type ProductFormData = z.infer<typeof productSchema>

// Customer validation schema
export const customerSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
})

export type CustomerFormData = z.infer<typeof customerSchema>

// Customer debt validation schema
export const customerDebtSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  productId: z.string().min(1, 'Product is required'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1'),
  dateOfDebt: z.date().optional(),
})

export type CustomerDebtFormData = z.infer<typeof customerDebtSchema>

// Payment validation schema
export const paymentSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  customerDebtId: z.string().optional(),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  notes: z.string().optional(),
  dateOfPayment: z.date().optional(),
})

export type PaymentFormData = z.infer<typeof paymentSchema>

// Categories for products
export const PRODUCT_CATEGORIES = [
  'Snacks',
  'Canned Goods',
  'Beverages',
  'Dairy',
  'Frozen Foods',
  'Personal Care',
  'Household Items',
  'Condiments',
  'Rice & Grains',
  'Noodles',
  'Other'
] as const

export type ProductCategory = typeof PRODUCT_CATEGORIES[number]
