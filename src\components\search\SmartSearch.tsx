'use client'

import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { Search, X, Filter, Clock, Package, Users, CreditCard } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useLocalStorage, useHydrationSafe } from '@/hooks/useHydrationSafe'

interface SearchResult {
  id: string
  title: string
  subtitle: string
  category: string
  url: string
  icon: React.ComponentType<{ className?: string }>
  metadata?: string
}

interface SmartSearchProps {
  placeholder?: string
  showFilters?: boolean
  onSearch?: (query: string, filters: SearchFilters) => void
  className?: string
}

interface SearchFilters {
  category: string
  dateRange: string
  status: string
  sortBy: string
}

export default function SmartSearch({
  placeholder = "Search products, customers, transactions...",
  showFilters = true,
  onSearch,
  className = ""
}: SmartSearchProps) {
  const [query, setQuery] = useState('')
  const [isOpen, setIsOpen] = useState(false)
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches, isRecentSearchesLoaded] = useLocalStorage<string[]>('recentSearches', [])
  const [isLoading, setIsLoading] = useState(false)
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const isHydrated = useHydrationSafe()
  const [filters, setFilters] = useState<SearchFilters>({
    category: 'all',
    dateRange: 'all',
    status: 'all',
    sortBy: 'relevance'
  })

  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Sample data - In production, this would come from your API
  const sampleResults = useMemo(() => [
    {
      id: '1',
      title: 'Coca Cola 1.5L',
      subtitle: 'Beverages • In Stock',
      category: 'products',
      url: '/products/1',
      icon: Package,
      metadata: '₱45.00 • 24 units'
    },
    {
      id: '2',
      title: 'Juan Dela Cruz',
      subtitle: 'Regular Customer',
      category: 'customers',
      url: '/customers/2',
      icon: Users,
      metadata: '₱1,250 total purchases'
    },
    {
      id: '3',
      title: 'Maria Santos - Outstanding Debt',
      subtitle: 'Due: Dec 15, 2024',
      category: 'debts',
      url: '/debts/3',
      icon: CreditCard,
      metadata: '₱350.00 remaining'
    }
  ], [])

  // Search function
  const performSearch = useCallback((searchQuery: unknown) => {
    const query = String(searchQuery)
    if (query.length > 0) {
      setIsLoading(true)
      // Simulate API call
      setTimeout(() => {
        const filtered = sampleResults.filter(item =>
          item.title.toLowerCase().includes(query.toLowerCase()) ||
          item.subtitle.toLowerCase().includes(query.toLowerCase())
        )
        setResults(filtered)
        setIsLoading(false)
      }, 300)
    } else {
      setResults([])
      setIsLoading(false)
    }
  }, [sampleResults])

  // Create debounced version using useRef to avoid dependency issues
  const debouncedSearchRef = useRef(debounce(performSearch, 300))

  // Update the debounced function when performSearch changes
  useEffect(() => {
    debouncedSearchRef.current = debounce(performSearch, 300)
  }, [performSearch])

  useEffect(() => {
    debouncedSearchRef.current(query)
  }, [query])

  // Recent searches are now handled by useLocalStorage hook

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSearch = (searchQuery: string) => {
    setQuery(searchQuery)
    setIsOpen(true)
    
    if (onSearch) {
      onSearch(searchQuery, filters)
    }
  }

  const handleResultClick = (result: SearchResult) => {
    // Save to recent searches (handled by useLocalStorage hook)
    const updated = [result.title, ...recentSearches.filter(s => s !== result.title)].slice(0, 5)
    setRecentSearches(updated)

    setIsOpen(false)
    router.push(result.url)
  }

  const clearSearch = () => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    inputRef.current?.focus()
  }



  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'products': return 'text-green-700 bg-green-100 border border-green-200'
      case 'customers': return 'text-blue-700 bg-blue-100 border border-blue-200'
      case 'debts': return 'text-red-700 bg-red-100 border border-red-200'
      case 'payments': return 'text-purple-700 bg-purple-100 border border-purple-200'
      default: return 'text-gray-700 bg-gray-100 border border-gray-200'
    }
  }

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      {/* Professional Search Input - Google/YouTube Style */}
      <div className="relative group">
        {/* Search Icon */}
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
          <Search className={`h-5 w-5 transition-colors duration-200 ${
            isOpen || query ? 'text-green-600' : 'text-gray-400 group-hover:text-gray-500'
          }`} />
        </div>

        {/* Main Search Input */}
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => handleSearch(e.target.value)}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          autoComplete="off"
          spellCheck="false"
          className={`
            block w-full pl-12 pr-16 py-3.5
            border-2 rounded-full
            bg-white text-gray-900 placeholder-gray-500
            font-medium text-base
            transition-all duration-300 ease-out
            shadow-sm hover:shadow-md
            ${isOpen || query
              ? 'border-green-500 ring-4 ring-green-100 shadow-lg'
              : 'border-gray-200 hover:border-gray-300 focus:border-green-500 focus:ring-4 focus:ring-green-100'
            }
            focus:outline-none
          `}
        />

        {/* Right Side Controls */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-2">
          {/* Clear Button */}
          {query && (
            <button
              onClick={clearSearch}
              className="p-2 rounded-full hover:bg-gray-100 active:bg-gray-200 transition-all duration-200 group/clear mr-1"
              title="Clear search"
            >
              <X className="h-4 w-4 text-gray-400 group-hover/clear:text-gray-600" />
            </button>
          )}

          {/* Filter Button */}
          {showFilters && (
            <button
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
              className={`
                p-2 rounded-full transition-all duration-200 group/filter
                ${showAdvancedFilters
                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                  : 'hover:bg-gray-100 text-gray-400 hover:text-gray-600'
                }
              `}
              title="Search filters"
            >
              <Filter className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Search Input Focus Ring Animation */}
        <div className={`
          absolute inset-0 rounded-full pointer-events-none
          transition-all duration-300 ease-out
          ${isOpen || query
            ? 'ring-2 ring-green-200 ring-opacity-50 scale-105'
            : 'ring-0 ring-transparent scale-100'
          }
        `} />
      </div>

      {/* Professional Advanced Filters */}
      {showAdvancedFilters && (
        <div className="absolute top-full left-0 right-0 mt-3 bg-white border border-gray-200 rounded-2xl shadow-2xl p-6 z-50 animate-fade-in backdrop-blur-sm">
          <div className="mb-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Search Filters</h3>
            <p className="text-sm text-gray-600">Refine your search results</p>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Category</label>
              <select
                value={filters.category}
                onChange={(e) => setFilters({...filters, category: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 hover:border-gray-400"
              >
                <option value="all">All Categories</option>
                <option value="products">Products</option>
                <option value="customers">Customers</option>
                <option value="debts">Debts</option>
                <option value="payments">Payments</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Date Range</label>
              <select
                value={filters.dateRange}
                onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 hover:border-gray-400"
              >
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="year">This Year</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Status</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 hover:border-gray-400"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
              </select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Sort By</label>
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters({...filters, sortBy: e.target.value})}
                className="w-full p-3 border border-gray-300 rounded-xl text-sm bg-white focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 hover:border-gray-400"
              >
                <option value="relevance">Relevance</option>
                <option value="date">Date</option>
                <option value="name">Name</option>
                <option value="amount">Amount</option>
              </select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={() => {
                setFilters({
                  category: 'all',
                  dateRange: 'all',
                  status: 'all',
                  sortBy: 'relevance'
                })
              }}
              className="text-sm text-gray-600 hover:text-gray-800 font-medium transition-colors"
            >
              Clear all filters
            </button>
            <button
              onClick={() => setShowAdvancedFilters(false)}
              className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-xl hover:bg-green-700 transition-colors duration-200"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Professional Search Results Dropdown - Google/YouTube Style */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-3 bg-white border border-gray-200 rounded-2xl shadow-2xl z-50 max-h-96 overflow-hidden animate-fade-in backdrop-blur-sm">
          {/* Loading State */}
          {isLoading && (
            <div className="p-6 text-center">
              <div className="inline-flex items-center space-x-3">
                <div className="w-5 h-5 border-2 border-green-500 border-t-transparent rounded-full animate-spin" />
                <span className="text-body-medium text-gray-600 font-medium">Searching...</span>
              </div>
            </div>
          )}

          {/* Professional Recent Searches */}
          {!query && isHydrated && isRecentSearchesLoaded && recentSearches.length > 0 && (
            <div className="p-5 border-b border-gray-100">
              <h3 className="text-sm font-semibold text-gray-800 mb-4 flex items-center">
                <Clock className="h-4 w-4 mr-2 text-gray-500" />
                Recent Searches
              </h3>
              <div className="space-y-1">
                {recentSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleSearch(search)}
                    className="group flex items-center w-full text-left px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-xl transition-all duration-200 hover:shadow-sm"
                  >
                    <Search className="h-4 w-4 mr-3 text-gray-400 group-hover:text-green-500 transition-colors" />
                    <span className="font-medium group-hover:text-gray-900">{search}</span>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Professional Search Results */}
          {results.length > 0 && (
            <div className="p-3 max-h-80 overflow-y-auto">
              {results.map((result, index) => {
                const IconComponent = result.icon
                return (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className="group w-full flex items-center space-x-4 p-4 hover:bg-gray-50 rounded-xl transition-all duration-200 hover:shadow-sm border border-transparent hover:border-gray-200"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className={`p-3 rounded-xl shadow-sm ${getCategoryColor(result.category)} group-hover:scale-110 transition-transform duration-200`}>
                      <IconComponent className="h-5 w-5" />
                    </div>
                    <div className="flex-1 text-left min-w-0">
                      <h4 className="text-base font-semibold text-gray-900 group-hover:text-green-700 transition-colors truncate">
                        {result.title}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1 truncate">{result.subtitle}</p>
                      {result.metadata && (
                        <p className="text-xs text-gray-500 mt-2 bg-gray-100 px-2 py-1 rounded-lg inline-block">
                          {result.metadata}
                        </p>
                      )}
                    </div>
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </button>
                )
              })}
            </div>
          )}

          {/* Professional No Results State */}
          {query && !isLoading && results.length === 0 && (
            <div className="p-10 text-center">
              <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto mb-4">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-600 mb-4">
                We couldn&apos;t find anything matching &ldquo;<span className="font-medium text-gray-900">{query}</span>&rdquo;
              </p>
              <div className="text-sm text-gray-500">
                <p>Try:</p>
                <ul className="mt-2 space-y-1">
                  <li>• Checking your spelling</li>
                  <li>• Using different keywords</li>
                  <li>• Adjusting your search filters</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

// Debounce utility function
function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
