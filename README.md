# Sari-Sari Store Admin Dashboard

A comprehensive web-based admin dashboard for managing a Sari-Sari store, built with Next.js 15, TypeScript, and Tailwind CSS.

## Features

### 📦 Product Management (CRUD Operations)
- Add, edit, and delete products
- Product image upload via Cloudinary
- Track product details: name, net weight, price, stock quantity, category
- Low stock alerts and inventory management
- Product categorization (Snacks, Canned Goods, Beverages, etc.)

### 👥 Customer Management (CRUD Operations)
- Register and manage customer information
- Track customer debt history
- View customer payment records
- Customer debt summary and analytics

### 💳 Customer Debt (Utang) Management (CRUD Operations)
- Record new customer debts with automatic stock deduction
- Track debt details: customer, product, quantity, amount, date
- Mark debts as paid/unpaid
- View debt history by customer
- Automatic calculation of total debt amounts

### 💰 Payment Processing
- Record customer payments
- Link payments to specific debts or general payments
- Payment history tracking
- Automatic debt balance calculations
- Payment date tracking with notes

### 📊 Admin Dashboard Features
- Real-time business analytics and statistics
- Quick action buttons for common tasks
- Overview of total products, customers, debts, and revenue
- Top customers by outstanding debt
- Low stock product alerts
- Business performance metrics

### 📈 Reports & Analytics
- Total revenue tracking
- Outstanding debt monitoring
- Payment rate analysis
- Customer debt rankings
- Inventory status reports
- Business summary dashboard

## Tech Stack

- **Frontend & Backend**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Database**: SQLite with Prisma ORM
- **Image Upload**: Cloudinary
- **Form Handling**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Date Handling**: date-fns

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd caparan-tindahan
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env` file in the root directory and add:
```env
DATABASE_URL="file:./dev.db"

# Cloudinary Configuration (Optional - for image uploads)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your_cloud_name"
CLOUDINARY_API_KEY="your_api_key"
CLOUDINARY_API_SECRET="your_api_secret"
```

4. Set up the database:
```bash
npx prisma generate
npx prisma db push
```

5. Seed the database with sample data:
```bash
npm run db:seed
```

6. Start the development server:
```bash
npm run dev
```

7. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Database Schema

The application uses the following main entities:

- **Products**: Store inventory items with pricing and stock information
- **Customers**: Customer information and contact details
- **CustomerDebts**: Records of customer purchases on credit
- **Payments**: Customer payment records and debt settlements

## API Endpoints

### Products
- `GET /api/products` - Get all products
- `POST /api/products` - Create new product
- `GET /api/products/[id]` - Get specific product
- `PUT /api/products/[id]` - Update product
- `DELETE /api/products/[id]` - Delete product

### Customers
- `GET /api/customers` - Get all customers with debt summary
- `POST /api/customers` - Create new customer
- `GET /api/customers/[id]` - Get specific customer with details
- `PUT /api/customers/[id]` - Update customer
- `DELETE /api/customers/[id]` - Delete customer

### Debts
- `GET /api/debts` - Get all debts (with optional filters)
- `POST /api/debts` - Create new debt record
- `GET /api/debts/[id]` - Get specific debt
- `PUT /api/debts/[id]` - Update debt status
- `DELETE /api/debts/[id]` - Delete debt record

### Payments
- `GET /api/payments` - Get all payments
- `POST /api/payments` - Record new payment

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes
│   ├── customers/         # Customer management pages
│   ├── debts/            # Debt management pages
│   ├── payments/         # Payment management pages
│   ├── products/         # Product management pages
│   └── reports/          # Reports and analytics
├── components/           # Reusable React components
├── lib/                 # Utility functions and configurations
│   ├── prisma.ts        # Prisma client setup
│   ├── cloudinary.ts    # Cloudinary configuration
│   └── validations.ts   # Zod validation schemas
└── prisma/              # Database schema and migrations
```

## Key Features Explained

### Debt Management System
- When a debt is recorded, product stock is automatically reduced
- Debts can be marked as paid/unpaid
- Payment history is tracked per customer
- Automatic calculation of remaining debt balances

### Inventory Management
- Real-time stock tracking
- Low stock alerts (≤10 units)
- Automatic stock deduction when debts are recorded
- Stock restoration when debts are deleted

### Payment Processing
- Payments can be linked to specific debts or recorded as general payments
- Automatic debt balance calculations
- Payment history with notes and dates
- Customer payment summaries

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please open an issue in the repository.
