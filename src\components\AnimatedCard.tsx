'use client'

import { useState, useEffect } from 'react'
import { LucideIcon } from 'lucide-react'

interface AnimatedCardProps {
  title: string
  value: string | number
  description: string
  icon: LucideIcon
  gradient: string
  bgColor: string
  textColor: string
  iconBg: string
  change: string
  changeType: 'positive' | 'negative'
  trend: number[]
  delay?: number
  loading?: boolean
}

export default function AnimatedCard({
  title,
  value,
  description,
  icon: Icon,
  gradient,
  iconBg,
  change,
  changeType,
  trend,
  delay = 0,
  loading = false
}: AnimatedCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, delay)

    return () => clearTimeout(timer)
  }, [delay])

  if (loading) {
    return (
      <div className="card-elevated p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="w-12 h-12 bg-gray-200 rounded-xl loading-shimmer"></div>
          <div className="w-16 h-6 bg-gray-200 rounded-full loading-shimmer"></div>
        </div>
        <div className="mb-4">
          <div className="w-24 h-4 bg-gray-200 rounded loading-shimmer mb-2"></div>
          <div className="w-20 h-8 bg-gray-200 rounded loading-shimmer mb-2"></div>
          <div className="w-32 h-3 bg-gray-200 rounded loading-shimmer"></div>
        </div>
        <div className="flex items-end space-x-1 h-8">
          {Array.from({ length: 7 }).map((_, i) => (
            <div
              key={i}
              className="bg-gray-200 rounded-sm loading-shimmer"
              style={{
                height: `${Math.random() * 100}%`,
                width: '8px'
              }}
            ></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div
      className={`
        group card-elevated p-6 overflow-hidden cursor-pointer
        hover-lift hover-glow
        ${isVisible ? 'animate-scale-in' : 'opacity-0'}
      `}
      style={{ animationDelay: `${delay}ms` }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Background Gradient Overlay */}
      <div 
        className={`
          absolute inset-0 bg-gradient-to-br ${gradient} 
          transition-opacity duration-500
          ${isHovered ? 'opacity-10' : 'opacity-0'}
        `}
      />

      {/* Floating Particles Effect */}
      {isHovered && (
        <>
          <div className="absolute top-4 right-4 w-2 h-2 bg-blue-400 rounded-full animate-float opacity-60" />
          <div className="absolute top-8 right-8 w-1 h-1 bg-purple-400 rounded-full animate-float opacity-40" style={{ animationDelay: '0.5s' }} />
          <div className="absolute top-6 right-12 w-1.5 h-1.5 bg-pink-400 rounded-full animate-float opacity-50" style={{ animationDelay: '1s' }} />
        </>
      )}

      {/* Card Content */}
      <div className="relative z-10">
        {/* Header with Icon and Change */}
        <div className="flex items-center justify-between mb-4">
          <div 
            className={`
              ${iconBg} p-3 rounded-xl shadow-lg 
              transition-all duration-300
              ${isHovered ? 'scale-110 rotate-3' : 'scale-100 rotate-0'}
            `}
          >
            <Icon className="h-6 w-6 text-white" />
          </div>
          <div 
            className={`
              flex items-center space-x-1 px-3 py-1.5 rounded-full text-xs font-semibold
              transition-all duration-300
              ${changeType === 'positive'
                ? 'bg-success-light text-success border border-green-200'
                : 'bg-error-light text-error border border-red-200'
              }
              ${isHovered ? 'scale-105 animate-pulse-gentle' : 'scale-100'}
            `}
          >
            <div className={`
              w-0 h-0 border-l-2 border-r-2 border-transparent
              transition-all duration-300
              ${changeType === 'positive' 
                ? 'border-b-4 border-b-green-600' 
                : 'border-t-4 border-t-red-600'
              }
              ${isHovered ? 'animate-bounce-gentle' : ''}
            `} />
            <span>{change}</span>
          </div>
        </div>

        {/* Title and Value */}
        <div className="mb-4">
          <p className="text-label-large text-muted mb-1 transition-colors duration-300 group-hover:text-gray-600">
            {title}
          </p>
          <div 
            className={`
              text-display-medium text-gray-900 mb-1 font-bold
              transition-all duration-300
              ${isHovered ? 'scale-105' : 'scale-100'}
            `}
          >
            {value}
          </div>
          <p className="text-body-small text-subtle transition-colors duration-300 group-hover:text-gray-500">
            {description}
          </p>
        </div>

        {/* Interactive Mini Sparkline */}
        <div className="flex items-end space-x-1 h-8">
          {trend.map((trendValue, i) => (
            <div
              key={i}
              className={`
                bg-gradient-to-t ${gradient} rounded-sm
                transition-all duration-500 ease-out
                ${isHovered ? 'opacity-90 scale-105' : 'opacity-60 scale-100'}
              `}
              style={{
                height: `${(trendValue / Math.max(...trend)) * 100}%`,
                width: '8px',
                animationDelay: `${i * 50}ms`,
                transform: isHovered ? `translateY(-2px) scaleY(1.1)` : 'translateY(0) scaleY(1)'
              }}
            />
          ))}
        </div>

        {/* Hover Reveal Content */}
        <div 
          className={`
            mt-4 pt-4 border-t border-gray-100
            transition-all duration-300 overflow-hidden
            ${isHovered ? 'max-h-20 opacity-100' : 'max-h-0 opacity-0'}
          `}
        >
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted">Last 7 days trend</span>
            <span className={`font-semibold ${changeType === 'positive' ? 'text-success' : 'text-error'}`}>
              {changeType === 'positive' ? '↗' : '↘'} {Math.abs(parseFloat(change.replace('%', '')))}% change
            </span>
          </div>
        </div>
      </div>

      {/* Interactive Shine Effect */}
      <div 
        className={`
          absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent
          opacity-0 transition-all duration-700
          ${isHovered ? 'opacity-20 translate-x-full' : 'opacity-0 -translate-x-full'}
        `}
        style={{
          background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent)',
          transform: isHovered ? 'translateX(100%)' : 'translateX(-100%)'
        }}
      />
    </div>
  )
}
