// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  imageUrl    String?
  netWeight   String
  price       Float
  stock       Int
  category    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relation to customer debts
  customerDebts CustomerDebt[]

  @@map("products")
}

model Customer {
  id          String   @id @default(cuid())
  firstName   String
  lastName    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  debts       CustomerDebt[]
  payments    Payment[]

  @@map("customers")
}

model CustomerDebt {
  id          String   @id @default(cuid())
  customerId  String
  productId   String
  productName String   // Store product name at time of debt
  productPrice Float   // Store product price at time of debt
  quantity    Int
  totalAmount Float    // productPrice * quantity
  dateOfDebt  DateTime @default(now())
  isPaid      Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)
  product     Product  @relation(fields: [productId], references: [id], onDelete: Restrict)
  payments    Payment[]

  @@map("customer_debts")
}

model Payment {
  id              String   @id @default(cuid())
  customerId      String
  customerDebtId  String?  // Optional: specific debt payment
  amount          Float
  dateOfPayment   DateTime @default(now())
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  customer        Customer      @relation(fields: [customerId], references: [id], onDelete: Cascade)
  customerDebt    CustomerDebt? @relation(fields: [customerDebtId], references: [id], onDelete: SetNull)

  @@map("payments")
}
