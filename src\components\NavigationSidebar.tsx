'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { LucideIcon } from 'lucide-react'
import {
  Package,
  Users,
  CreditCard,
  Receipt,
  BarChart3,
  Home,
  Store,
  Settings,
  X
} from 'lucide-react'

interface NavigationItem {
  name: string
  href: string
  icon: LucideIcon
}

const navigationItems: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Products', href: '/products', icon: Package },
  { name: 'Customers', href: '/customers', icon: Users },
  { name: 'Debts', href: '/debts', icon: CreditCard },
  { name: 'Payments', href: '/payments', icon: Receipt },
  { name: 'Reports', href: '/reports', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
]

interface NavigationSidebarProps {
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  isMobileMenuOpen?: boolean
  onMobileMenuToggle?: () => void
}

export default function NavigationSidebar({
  isCollapsed = false,
  onToggleCollapse: _onToggleCollapse,
  isMobileMenuOpen = false,
  onMobileMenuToggle
}: NavigationSidebarProps) {
  // Suppress unused variable warning - parameter kept for interface consistency
  void _onToggleCollapse
  const pathname = usePathname()

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => onMobileMenuToggle?.()}
        />
      )}

      {/* YouTube-Style Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 z-50 h-full bg-white border-r border-gray-200 transition-all duration-300
          ${isCollapsed ? 'w-16' : 'w-64'}
          ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
        `}
      >
        {/* Header */}
        <div className={`flex items-center h-14 px-4 border-b border-gray-200 ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <Store className="h-4 w-4 text-white" />
              </div>
              <div>
                <h1 className="text-base font-semibold text-gray-900">Caparan</h1>
                <p className="text-xs text-gray-500">Tindahan</p>
              </div>
            </div>
          )}

          {isCollapsed && (
            <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
              <Store className="h-4 w-4 text-white" />
            </div>
          )}

          {/* Mobile Close Button */}
          {isMobileMenuOpen && (
            <button
              onClick={() => onMobileMenuToggle?.()}
              className="lg:hidden p-2 rounded-full hover:bg-gray-100"
            >
              <X className="h-4 w-4 text-gray-600" />
            </button>
          )}
        </div>

        {/* Navigation Content */}
        <div className="flex-1 py-4 overflow-y-auto">
          <div className={`${isCollapsed ? 'px-2' : 'px-3'} space-y-1`}>
            {navigationItems.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => isMobileMenuOpen && onMobileMenuToggle?.()}
                  className={`
                    flex items-center rounded-lg transition-colors duration-150
                    ${isCollapsed ? 'p-3 justify-center' : 'px-3 py-2'}
                    ${isActive 
                      ? 'bg-gray-100 text-gray-900' 
                      : 'text-gray-700 hover:bg-gray-100'
                    }
                  `}
                  title={isCollapsed ? item.name : undefined}
                >
                  <item.icon className={`${isCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'} ${
                    isActive ? 'text-gray-900' : 'text-gray-600'
                  }`} />
                  {!isCollapsed && (
                    <span className="text-sm font-medium">{item.name}</span>
                  )}
                </Link>
              )
            })}
          </div>
        </div>
      </aside>
    </>
  )
}
