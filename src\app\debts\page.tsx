'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import ClientOnlyLayout from '@/components/ClientOnlyLayout'
import { Plus, Trash2, CreditCard, CheckCircle, XCircle } from 'lucide-react'

interface Debt {
  id: string
  productName: string
  productPrice: number
  quantity: number
  totalAmount: number
  dateOfDebt: string
  isPaid: boolean
  customer: {
    id: string
    firstName: string
    lastName: string
  }
  product: {
    id: string
    name: string
  }
}

export default function DebtsPage() {
  const [debts, setDebts] = useState<Debt[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'paid' | 'unpaid'>('all')

  useEffect(() => {
    fetchDebts()
  }, [])

  const fetchDebts = async () => {
    try {
      const response = await fetch('/api/debts')
      const data = await response.json()
      setDebts(data)
    } catch (error) {
      console.error('Error fetching debts:', error)
    } finally {
      setLoading(false)
    }
  }

  const deleteDebt = async (id: string) => {
    if (!confirm('Are you sure you want to delete this debt? This will restore the product stock.')) return

    try {
      const response = await fetch(`/api/debts/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setDebts(debts.filter(debt => debt.id !== id))
      } else {
        alert('Failed to delete debt')
      }
    } catch (error) {
      console.error('Error deleting debt:', error)
      alert('Failed to delete debt')
    }
  }

  const togglePaidStatus = async (id: string, isPaid: boolean) => {
    try {
      const response = await fetch(`/api/debts/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPaid: !isPaid }),
      })

      if (response.ok) {
        setDebts(debts.map(debt => 
          debt.id === id ? { ...debt, isPaid: !isPaid } : debt
        ))
      } else {
        alert('Failed to update debt status')
      }
    } catch (error) {
      console.error('Error updating debt:', error)
      alert('Failed to update debt status')
    }
  }

  const filteredDebts = debts.filter(debt => {
    if (filter === 'paid') return debt.isPaid
    if (filter === 'unpaid') return !debt.isPaid
    return true
  })

  return (
    <ClientOnlyLayout
      title="Customer Debts"
      subtitle="Manage customer debt records"
    >
      <div className="mb-6 flex justify-between items-center">
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              filter === 'all' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All Debts
          </button>
          <button
            onClick={() => setFilter('unpaid')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              filter === 'unpaid' 
                ? 'bg-red-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Unpaid
          </button>
          <button
            onClick={() => setFilter('paid')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              filter === 'paid' 
                ? 'bg-green-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Paid
          </button>
        </div>
        
        <Link
          href="/debts/new"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Record New Debt
        </Link>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="text-gray-500">Loading debts...</div>
        </div>
      ) : filteredDebts.length === 0 ? (
        <div className="text-center py-8">
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {filter === 'all' ? 'No debts found' : `No ${filter} debts found`}
          </h3>
          <p className="text-gray-500 mb-4">
            {filter === 'all' 
              ? 'Get started by recording your first debt.' 
              : `There are no ${filter} debts at the moment.`
            }
          </p>
          {filter === 'all' && (
            <Link
              href="/debts/new"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-5 w-5 mr-2" />
              Record Debt
            </Link>
          )}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Quantity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredDebts.map((debt) => (
                  <tr key={debt.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {debt.customer.firstName} {debt.customer.lastName}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{debt.productName}</div>
                      <div className="text-sm text-gray-500">₱{debt.productPrice.toFixed(2)} each</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {debt.quantity}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ₱{debt.totalAmount.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(debt.dateOfDebt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        debt.isPaid 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {debt.isPaid ? 'Paid' : 'Unpaid'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => togglePaidStatus(debt.id, debt.isPaid)}
                          className={`${
                            debt.isPaid 
                              ? 'text-red-600 hover:text-red-900' 
                              : 'text-green-600 hover:text-green-900'
                          }`}
                          title={debt.isPaid ? 'Mark as Unpaid' : 'Mark as Paid'}
                        >
                          {debt.isPaid ? <XCircle className="h-4 w-4" /> : <CheckCircle className="h-4 w-4" />}
                        </button>
                        <button
                          onClick={() => deleteDebt(debt.id)}
                          className="text-red-600 hover:text-red-900"
                          title="Delete"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </ClientOnlyLayout>
  )
}
