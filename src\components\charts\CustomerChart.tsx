'use client'

import { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts'
import { Users, UserCheck, UserX, Star } from 'lucide-react'

interface CustomerData {
  month: string
  newCustomers: number
  activeCustomers: number
  churnedCustomers: number
  satisfaction: number
}

interface CustomerChartProps {
  data: CustomerData[]
  title?: string
  height?: number
}

export default function CustomerChart({
  data,
  title = "Customer Analytics",
  height = 350
}: CustomerChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'growth' | 'satisfaction'>('growth')

  // Calculate metrics
  const totalNewCustomers = data.reduce((sum, item) => sum + item.newCustomers, 0)
  const avgActiveCustomers = data.reduce((sum, item) => sum + item.activeCustomers, 0) / data.length
  const totalChurned = data.reduce((sum, item) => sum + item.churnedCustomers, 0)
  const avgSatisfaction = data.reduce((sum, item) => sum + item.satisfaction, 0) / data.length
  const retentionRate = (((avgActiveCustomers - totalChurned) / avgActiveCustomers) * 100).toFixed(1)

  useEffect(() => {
    if (!chartRef.current || !data.length) return

    chartInstance.current = echarts.init(chartRef.current, 'light')

    const growthOption: echarts.EChartsOption = {
      title: {
        text: 'Customer Growth',
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        }
      },
      legend: {
        data: ['New Customers', 'Active Customers', 'Churned'],
        top: 30,
        textStyle: {
          color: '#6b7280'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.month),
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280'
        }
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280'
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6'
          }
        }
      },
      series: [
        {
          name: 'New Customers',
          type: 'bar',
          data: data.map(item => item.newCustomers),
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#34d399' },
              { offset: 1, color: '#10b981' }
            ]),
            borderRadius: [4, 4, 0, 0]
          }
        },
        {
          name: 'Active Customers',
          type: 'line',
          data: data.map(item => item.activeCustomers),
          lineStyle: {
            color: '#3b82f6',
            width: 3
          },
          itemStyle: {
            color: '#3b82f6'
          },
          symbol: 'circle',
          symbolSize: 6
        },
        {
          name: 'Churned',
          type: 'line',
          data: data.map(item => item.churnedCustomers),
          lineStyle: {
            color: '#ef4444',
            width: 2,
            type: 'dashed'
          },
          itemStyle: {
            color: '#ef4444'
          },
          symbol: 'diamond',
          symbolSize: 5
        }
      ]
    }

    const satisfactionOption: echarts.EChartsOption = {
      title: {
        text: 'Customer Satisfaction',
        left: 'center',
        textStyle: {
          fontSize: 14,
          fontWeight: 'bold',
          color: '#374151'
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        textStyle: {
          color: '#374151'
        },
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formatter: function (params: any) {
          const data = params[0]
          return `
            <div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 8px;">${data.axisValue}</div>
              <div style="margin: 4px 0;"><span style="color: #8b5cf6;">●</span> Satisfaction: ${data.value}/5.0</div>
            </div>
          `
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.month),
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280'
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 5,
        axisLine: {
          lineStyle: {
            color: '#e5e7eb'
          }
        },
        axisLabel: {
          color: '#6b7280',
          formatter: '{value}/5'
        },
        splitLine: {
          lineStyle: {
            color: '#f3f4f6'
          }
        }
      },
      series: [
        {
          name: 'Satisfaction',
          type: 'line',
          data: data.map(item => item.satisfaction),
          lineStyle: {
            color: '#8b5cf6',
            width: 4
          },
          itemStyle: {
            color: '#8b5cf6'
          },
          symbol: 'circle',
          symbolSize: 8,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(139, 92, 246, 0.3)' },
              { offset: 1, color: 'rgba(139, 92, 246, 0.1)' }
            ])
          },
          smooth: true
        }
      ]
    }

    const option = activeTab === 'growth' ? growthOption : satisfactionOption
    chartInstance.current.setOption(option)
    
    setTimeout(() => setIsLoading(false), 400)

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chartInstance.current?.dispose()
    }
  }, [data, title, height, activeTab])

  return (
    <div className="card-elevated p-6 hover-lift animate-fade-in stagger-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="bg-gradient-to-r from-purple-500 to-indigo-600 p-2 rounded-lg">
            <Users className="h-5 w-5 text-white" />
          </div>
          <div>
            <h3 className="text-heading-medium text-gray-900">{title}</h3>
            <p className="text-body-small text-muted">Customer insights and trends</p>
          </div>
        </div>

        {/* Tab Selector */}
        <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('growth')}
            className={`
              px-3 py-1 text-xs font-medium rounded-md transition-all duration-200
              ${activeTab === 'growth'
                ? 'bg-white text-primary shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
          >
            Growth
          </button>
          <button
            onClick={() => setActiveTab('satisfaction')}
            className={`
              px-3 py-1 text-xs font-medium rounded-md transition-all duration-200
              ${activeTab === 'satisfaction'
                ? 'bg-white text-primary shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
          >
            Satisfaction
          </button>
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-purple-600 border-t-transparent rounded-full animate-spin" />
              <span className="text-body-medium text-muted">Loading customer data...</span>
            </div>
          </div>
        )}
        <div
          ref={chartRef}
          style={{ height: `${height}px`, width: '100%' }}
          className="transition-opacity duration-300"
        />
      </div>

      {/* Metrics Summary */}
      <div className="mt-6 grid grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <UserCheck className="h-4 w-4 text-success" />
            <p className="text-body-small text-muted">New Customers</p>
          </div>
          <p className="text-heading-small text-gray-900">{totalNewCustomers.toLocaleString()}</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Users className="h-4 w-4 text-blue-600" />
            <p className="text-body-small text-muted">Avg Active</p>
          </div>
          <p className="text-heading-small text-gray-900">{Math.round(avgActiveCustomers).toLocaleString()}</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <UserX className="h-4 w-4 text-error" />
            <p className="text-body-small text-muted">Retention Rate</p>
          </div>
          <p className="text-heading-small text-success">{retentionRate}%</p>
        </div>
        <div className="text-center">
          <div className="flex items-center justify-center space-x-1 mb-1">
            <Star className="h-4 w-4 text-yellow-500" />
            <p className="text-body-small text-muted">Avg Satisfaction</p>
          </div>
          <p className="text-heading-small text-purple-600">{avgSatisfaction.toFixed(1)}/5.0</p>
        </div>
      </div>
    </div>
  )
}
