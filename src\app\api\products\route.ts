import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { productSchema } from '@/lib/validations'

// GET /api/products - Get all products
export async function GET() {
  try {
    const products = await prisma.product.findMany({
      orderBy: { createdAt: 'desc' },
    })

    // If no products found, return sample data for demo purposes
    if (products.length === 0) {
      const sampleProducts = [
        {
          id: 'sample-1',
          name: 'Coca Cola 1.5L',
          description: 'Refreshing cola drink',
          price: 45.00,
          stock: 24,
          category: 'Beverages',
          barcode: '123456789',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'sample-2',
          name: 'Lucky Me Pancit Canton',
          description: 'Instant noodles',
          price: 12.50,
          stock: 48,
          category: 'Food',
          barcode: '987654321',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }
      ]
      return NextResponse.json(sampleProducts)
    }

    return NextResponse.json(products)
  } catch (error) {
    console.error('Error fetching products:', error)
    // Return empty array instead of error to prevent dashboard crashes
    return NextResponse.json([])
  }
}

// POST /api/products - Create a new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = productSchema.parse(body)

    const product = await prisma.product.create({
      data: validatedData,
    })

    return NextResponse.json(product, { status: 201 })
  } catch (error) {
    console.error('Error creating product:', error)
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Invalid product data', details: error },
        { status: 400 }
      )
    }
    return NextResponse.json(
      { error: 'Failed to create product' },
      { status: 500 }
    )
  }
}
