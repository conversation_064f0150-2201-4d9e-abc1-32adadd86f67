'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { LucideIcon } from 'lucide-react'
import {
  Package,
  Users,
  CreditCard,
  Receipt,
  BarChart3,
  Home,
  Store,
  Settings,
  X,
  Plus,
  FileText,
  TrendingUp,
  Clock,
  Star,
  Menu,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { useNavigationData } from '@/hooks/useNavigationData'
import { useState } from 'react'
import { useHydrationSafe } from '@/hooks/useHydrationSafe'

interface NavigationItem {
  name: string
  href: string
  icon: LucideIcon
  badge?: string | number
  isNew?: boolean
}

interface NavigationSection {
  title?: string
  items: NavigationItem[]
  collapsible?: boolean
  defaultExpanded?: boolean
}

interface YoutubeSidebarProps {
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  isMobileMenuOpen?: boolean
  onMobileMenuToggle?: () => void
}

export default function YoutubeSidebar({
  isCollapsed = false,
  onToggleCollapse,
  isMobileMenuOpen = false,
  onMobileMenuToggle
}: YoutubeSidebarProps) {
  const pathname = usePathname()
  const navigationData = useNavigationData()
  const isHydrated = useHydrationSafe()
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    main: true,
    business: true,
    reports: true,
    quickActions: false,
    recent: false
  })

  // Don't render until hydrated to prevent SSR/client mismatch
  if (!isHydrated) {
    return (
      <aside className="fixed top-0 left-0 z-50 h-full w-64 bg-white border-r border-gray-200 -translate-x-full lg:translate-x-0">
        <div className="flex items-center h-14 px-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-300 rounded-lg animate-pulse"></div>
            <div>
              <div className="h-4 bg-gray-300 rounded w-24 mb-1 animate-pulse"></div>
              <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
        </div>
        <div className="p-4 space-y-2">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="flex items-center space-x-3 p-2">
              <div className="w-5 h-5 bg-gray-300 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-300 rounded flex-1 animate-pulse"></div>
            </div>
          ))}
        </div>
      </aside>
    )
  }

  // YouTube-style navigation sections with professional organization
  const mainNavigation: NavigationItem[] = [
    { name: 'Dashboard', href: '/dashboard', icon: Home },
    { name: 'Analytics', href: '/analytics', icon: BarChart3 },
  ]

  const businessNavigation: NavigationItem[] = [
    {
      name: 'Products',
      href: '/products',
      icon: Package,
      badge: navigationData.loading ? '' : navigationData.productsCount.toString()
    },
    {
      name: 'Customers',
      href: '/customers',
      icon: Users,
      badge: navigationData.loading ? '' : navigationData.customersCount.toString()
    },
    {
      name: 'Debts',
      href: '/debts',
      icon: CreditCard,
      badge: navigationData.loading ? '' : navigationData.debtsCount.toString()
    },
    {
      name: 'Payments',
      href: '/payments',
      icon: Receipt,
      badge: navigationData.loading ? '' : navigationData.paymentsCount.toString()
    },
  ]

  const reportsNavigation: NavigationItem[] = [
    { name: 'Reports', href: '/reports', icon: FileText },
    { name: 'Analytics', href: '/analytics', icon: TrendingUp },
  ]

  const quickActions: NavigationItem[] = [
    { name: 'Add Product', href: '/products/new', icon: Plus, isNew: true },
    { name: 'Add Customer', href: '/customers/new', icon: Users },
    { name: 'Record Payment', href: '/payments/new', icon: Receipt },
    { name: 'Record Debt', href: '/debts/new', icon: CreditCard },
  ]

  const recentItems: NavigationItem[] = [
    { name: 'Recent Products', href: '/products', icon: Clock },
    { name: 'Recent Customers', href: '/customers', icon: Clock },
    { name: 'Recent Payments', href: '/payments', icon: Clock },
  ]

  // YouTube-style section organization
  const navigationSections: NavigationSection[] = [
    { items: mainNavigation },
    { title: 'Store Management', items: businessNavigation, collapsible: false, defaultExpanded: true },
    { title: 'Reports & Analytics', items: reportsNavigation, collapsible: true, defaultExpanded: true },
    { title: 'Quick Actions', items: quickActions, collapsible: true, defaultExpanded: false },
    { title: 'Recent Activity', items: recentItems, collapsible: true, defaultExpanded: false },
  ]

  const toggleSection = (sectionKey: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }))
  }

  // YouTube-style navigation item renderer
  const renderNavigationItem = (item: NavigationItem, sectionKey?: string) => {
    const isActive = pathname === item.href
    const showBadge = item.badge && item.badge !== '0' && !isCollapsed && !navigationData.loading
    const showNewIndicator = item.isNew && !isCollapsed

    return (
      <Link
        key={item.href}
        href={item.href}
        onClick={() => isMobileMenuOpen && onMobileMenuToggle?.()}
        className={`
          group relative flex items-center transition-all duration-200 youtube-nav-item
          ${isCollapsed
            ? 'p-3 justify-center mx-1 rounded-lg'
            : 'px-3 py-2 mx-3 rounded-lg'
          }
          ${isActive
            ? 'bg-gray-100 text-gray-900 youtube-nav-active font-medium'
            : 'text-gray-700 hover:bg-gray-100'
          }
        `}
        title={isCollapsed ? item.name : undefined}
        aria-label={isCollapsed ? item.name : undefined}
      >
        <item.icon className={`
          ${isCollapsed ? 'w-6 h-6' : 'w-5 h-5 mr-3'}
          ${isActive ? 'text-gray-900' : 'text-gray-600 group-hover:text-gray-900'}
          transition-colors duration-200 flex-shrink-0
        `} />

        {!isCollapsed && (
          <>
            <span className="text-sm font-medium flex-1 truncate">{item.name}</span>

            {/* YouTube-style Badge */}
            {showBadge && (
              <span className="text-xs px-1.5 py-0.5 bg-gray-200 text-gray-700 rounded-full ml-auto font-medium min-w-[20px] text-center">
                {item.badge}
              </span>
            )}

            {/* Loading state for badges */}
            {navigationData.loading && item.badge && (
              <div className="w-6 h-4 bg-gray-200 rounded-full ml-auto animate-pulse"></div>
            )}

            {/* New Indicator */}
            {showNewIndicator && (
              <span className="ml-2 w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></span>
            )}
          </>
        )}

        {/* YouTube-style Tooltip for collapsed state */}
        {isCollapsed && (
          <div className="absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
            <div className="flex items-center">
              {item.name}
              {item.badge && item.badge !== '0' && !navigationData.loading && (
                <span className="ml-2 px-1.5 py-0.5 bg-gray-700 rounded text-xs">
                  {item.badge}
                </span>
              )}
            </div>
            {/* Tooltip arrow */}
            <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45"></div>
          </div>
        )}
      </Link>
    )
  }

  // YouTube-style section header renderer
  const renderSectionHeader = (section: NavigationSection, sectionKey: string) => {
    if (!section.title || isCollapsed) return null

    const isExpanded = expandedSections[sectionKey]
    const ChevronIcon = isExpanded ? ChevronDown : ChevronRight

    return (
      <div className="px-3 mb-1">
        {section.collapsible ? (
          <button
            onClick={() => toggleSection(sectionKey)}
            className="flex items-center w-full text-xs font-medium text-gray-600 uppercase tracking-wide hover:text-gray-800 transition-colors duration-200 py-2 group"
            aria-expanded={isExpanded}
            aria-controls={`section-${sectionKey}`}
          >
            <ChevronIcon className="w-4 h-4 mr-1 transition-transform duration-200 group-hover:scale-110" />
            {section.title}
          </button>
        ) : (
          <h3 className="text-xs font-medium text-gray-600 uppercase tracking-wide py-2 border-b border-gray-100 mb-2">
            {section.title}
          </h3>
        )}
      </div>
    )
  }

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 backdrop-blur-sm"
          onClick={() => onMobileMenuToggle?.()}
          aria-hidden="true"
        />
      )}

      {/* YouTube-Style Sidebar */}
      <aside
        className={`
          fixed top-0 left-0 z-50 h-full bg-white border-r border-gray-200 transition-all duration-300 youtube-sidebar
          ${isCollapsed ? 'w-16' : 'w-64'}
          ${isMobileMenuOpen ? 'translate-x-0 shadow-2xl' : '-translate-x-full lg:translate-x-0'}
        `}
        aria-label="Main navigation"
      >
        {/* YouTube-style Header */}
        <div className={`
          flex items-center h-14 px-4 border-b border-gray-100 bg-white sticky top-0 z-10
          ${isCollapsed ? 'justify-center' : 'justify-between'}
        `}>
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className="w-9 h-9 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center shadow-sm">
                <Store className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-base font-semibold text-gray-900 leading-tight">Caparan Tindahan</h1>
                <p className="text-xs text-gray-500 leading-tight">Admin Dashboard</p>
              </div>
            </div>
          )}

          {!isCollapsed && (
            <button
              onClick={() => onToggleCollapse?.()}
              className="hidden lg:flex p-2 rounded-full hover:bg-gray-100 transition-all duration-200 hover:scale-105"
              title="Collapse sidebar"
              aria-label="Collapse sidebar"
            >
              <Menu className="h-4 w-4 text-gray-600" />
            </button>
          )}

          {isCollapsed && (
            <button
              onClick={() => onToggleCollapse?.()}
              className="w-9 h-9 bg-gradient-to-br from-green-600 to-green-700 rounded-xl flex items-center justify-center shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105"
              title="Expand sidebar"
              aria-label="Expand sidebar"
            >
              <Store className="h-5 w-5 text-white" />
            </button>
          )}

          {/* Mobile Close Button */}
          {isMobileMenuOpen && (
            <button
              onClick={() => onMobileMenuToggle?.()}
              className="lg:hidden p-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
              aria-label="Close navigation menu"
            >
              <X className="h-4 w-4 text-gray-600" />
            </button>
          )}
        </div>

        {/* YouTube-style Navigation Content */}
        <div className="flex-1 overflow-y-auto youtube-sidebar">
          <nav className="py-2" role="navigation" aria-label="Sidebar navigation">
            {navigationSections.map((section, sectionIndex) => {
              const sectionKey = section.title?.toLowerCase().replace(/\s+/g, '').replace(/&/g, '') || `section-${sectionIndex}`
              const isExpanded = section.collapsible ? expandedSections[sectionKey] : true

              return (
                <div key={sectionKey} className={`${sectionIndex > 0 ? 'mt-4' : 'mt-2'}`}>
                  {renderSectionHeader(section, sectionKey)}

                  <div
                    id={`section-${sectionKey}`}
                    className={`
                      transition-all duration-300 overflow-hidden
                      ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}
                    `}
                  >
                    <div className={`space-y-0.5 ${!isCollapsed ? 'mb-2' : ''}`}>
                      {section.items.map((item) => renderNavigationItem(item, sectionKey))}
                    </div>
                  </div>

                  {/* YouTube-style Section Divider */}
                  {!isCollapsed && sectionIndex < navigationSections.length - 1 && isExpanded && section.title && (
                    <div className="mx-3 mt-3 mb-1 border-b border-gray-100"></div>
                  )}
                </div>
              )
            })}
          </nav>

          {/* YouTube-style Footer */}
          {!isCollapsed && (
            <div className="px-3 py-3 border-t border-gray-100 mt-2">
              <div className="flex items-center space-x-3 p-3 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-150 transition-all duration-200">
                <div className="w-9 h-9 bg-gradient-to-br from-green-600 to-green-700 rounded-full flex items-center justify-center shadow-sm">
                  <Store className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900 truncate">Store Admin</p>
                  <p className="text-xs text-gray-500 truncate">Caparan Tindahan</p>
                </div>
                <Link
                  href="/settings"
                  className="p-2 rounded-full hover:bg-white hover:shadow-sm transition-all duration-200 group"
                  title="Settings"
                  aria-label="Go to settings"
                >
                  <Settings className="h-4 w-4 text-gray-600 group-hover:text-gray-800 transition-colors duration-200" />
                </Link>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}
