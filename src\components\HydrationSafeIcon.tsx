'use client'

import { useHydrationSafe } from '@/hooks/useHydrationSafe'
import { LucideIcon } from 'lucide-react'

interface HydrationSafeIconProps {
  icon: LucideIcon
  className?: string
  size?: number
  fallback?: React.ReactNode
}

/**
 * Wrapper component for Lucide React icons to prevent hydration mismatches
 * This ensures consistent rendering between server and client
 */
export default function HydrationSafeIcon({ 
  icon: Icon, 
  className = "", 
  size,
  fallback 
}: HydrationSafeIconProps) {
  const isHydrated = useHydrationSafe()

  // Show fallback during SSR to prevent hydration mismatch
  if (!isHydrated) {
    return fallback || (
      <div 
        className={`inline-block ${className}`}
        style={{ 
          width: size || '1em', 
          height: size || '1em',
          backgroundColor: 'transparent'
        }}
      />
    )
  }

  // Render the actual icon after hydration
  return <Icon className={className} size={size} />
}

/**
 * Higher-order component to create hydration-safe versions of Lucide icons
 */
export function createHydrationSafeIcon(icon: LucideIcon) {
  return function HydrationSafeIconComponent(props: { className?: string; size?: number }) {
    return <HydrationSafeIcon icon={icon} {...props} />
  }
}
